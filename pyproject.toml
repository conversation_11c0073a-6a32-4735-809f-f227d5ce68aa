[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "QFlowAgent"
version = "0.1.0"
description = "QFlowAgent"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "langgraph>=0.5.4",
    "langchain>=0.3.27",
    "dotenv",
    "langchain-openai>=0.3.28",
    "langchain-tavily>=0.2.11",
    "httpx[socks]>=0.27.0",
    "langgraph-api>=0.2.102",
    "json-repair>=0.48.0",
    "langchain-experimental>=0.3.4",
    "markdownify>=1.1.0",
    "readabilipy>=0.3.0",
    "duckduckgo-search>=8.1.1",
    "langchain-community>=0.3.27",
    "psycopg[binary,pool]>=3.2.9",
    "psycopg-pool>=3.2.6",
    "langgraph-checkpoint-postgres>=2.0.23",
    "langchain-mcp-adapters>=0.1.9",
    "pymysql>=1.1.0",
    "aiomysql>=0.2.0",
    "fastapi>=0.104.0",
    "uvicorn[standard]>=0.24.0",
    "PySocks>=1.7.1",
    "langchain-postgres>=0.0.15",
    "dashscope>=1.24.0",
    "greenlet>=3.2.3",
    "langchain-deepseek>=0.1.4",
    "pandas>=2.3.1",
    "sqlparse>=0.5.3",
    "tabulate>=0.9.0",
    "langgraph-cli[inmem]>=0.3.6",
    "lark-oapi>=1.4.20",
    "pytest-asyncio>=1.1.0",
    "ruff>=0.12.9",
    "sseclient>=0.0.27",
]

[project.optional-dependencies]
test = [
    "pytest",
    "pytest-cov",
    "pytest-asyncio",
    "vcrpy",
]
dev = [
    "langgraph-cli[inmem]" 
]

[tool.setuptools.packages.find]
where = ["."]
include = ["src*"]

[tool.black]
line-length = 120

[tool.ruff]
line-length = 120

[dependency-groups]
dev = [
    "pytest>=8.4.1",
    "ruff>=0.12.9",
]

[tool.pytest.ini_options]
testpaths = ["tests"]
python_files = ["test_*.py"]
