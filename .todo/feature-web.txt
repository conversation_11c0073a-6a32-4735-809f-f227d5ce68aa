------ round1
现在程序只支持cli执行，很难观测，现在需要提供一个前端给用户
1. 能够使用前端启动任务
2. 能够使用前端查询任务历史、任务详情
3. 历史功能使用langgraph的checkpoint来实现

规划如下： 
前端：
代码位置：./src/web
启动脚本： ./scripts/run_web.sh
技术栈：pnpm vue element-plus pinia  vite


中间层：
代码位置：./src/fastapi
启动脚本： ./scripts/run_web.sh
技术栈：fast-api
feature： 写一个checkpointManager，

现在请评估一个方案



------ round2
补充小细节：
1. 页面默认dark mode，element-plus有官方的darkmode支持
2. 由fast-api的service层 从langgraph获取checkpointer, 并使用checkpointer.alist({})获取历史



------ round3 
补充前后端交互细节要求
1. 由前端启动的任务，后端需要使用StreamingResponse，调用astream时，参考这个代码，数据打包和解析你可以使用你的方案
```python
        # values: 每个节点触发一次，并行节点合并触发一次；将state返回上来
        # message: human的没有，llm请求是流式的token by token;
        # updates: 返回每个节点的upate内容
        # custom: 由langgraph内部get_stream_writer()('custom info') 传上来
        stream_mode = ["values", "message", "updates", "custom"]
        async for ev in workflow.astream(inputs, config, stream_mode=stream_mode)
            if isinstance(ev, tuple) and len(ev) == 2:
                etype, evalue = ev
            else:
                etype, evalue = "updates", ev
            event_obj = {"type": etype, "value": evalue, "request_id": rid}
            yield f"data: {json.dumps(event_obj, ensure_ascii=False)}\n\n"
        yield f"data: {json.dumps({'type': 'success', 'value': None, 'request_id': rid}, ensure_ascii=False)}\n\n"
    except Exception as e:
        err_obj = {"type": "error", "value": str(e), "request_id": rid}
        yield f"data: {json.dumps(err_obj, ensure_ascii=False)}\n\n"
    finally:
        yield f"data: {json.dumps({'type': 'close', 'value': None, 'request_id': rid}, ensure_ascii=False)}\n\n"

headers = {"Cache-Control": "no-cache", "Connection": "keep-alive"}
return StreamingResponse(event_gen(), media_type="text/event-stream", headers=headers)
```
2. 前端使用EventSource或你认为更合理的方式来接收流式数据，展示进展





------ round2-1
继续，顺便补充细节：
端口：
- 前端端口支持轮换，如果已占用则递增
- 后端地址固定2026

你可以使用浏览器工具来查看前端，使用命令行启动后端与前端，你需要工作直到功能调通，包括不限于
1. 创建新任务，能够看到langgraph每个task抛出的信息
2. 查看任务历史，理论上能够看到和


------ round2-1
后端日志显示有非常多的报错，你只关注了前端，这是非常危险的调试行为！

遇到了数据库连接的问题
1. 启动前端后，需要进行基本的功能测试，执行一个最简单的select 1，查看数据库是否能够连通
2. 用户启动任务前，也需要确认select 1连通正常
3. 任务列表和任务详情也需要确认数据是正常捞上来的

另外，前端也没有展示详细的分析结论，大模型返回的内容也没有渲染出来

------ round2-2
code review:
1. 在你的test_database_connection中，你使用底层代码建立了一个连接实例，这是非常危险的
你应该使用 ocean_connection.test_connection()
2. 现在的后端非常危险，希望添加一个任务终止的检查：如果数据库连接失败，则结束这个langgrap任务


------ round3-3
code review:
1. 应该将健康检查单独设置为一个task， 在entropoint中执行, 不要放到planner中
2. 深入调查这个问题：我们很担心影响到线上数据库

2025-08-18 19:04:12,406 [src.langgraph.tasks.task_planner] INFO: [planner] 开始批量查询数据
2025-08-18 19:04:12,461 [src.langgraph.zego_tools.ocean_executor.connector] ERROR: [ocean] 创建连接池失败: (2013, 'Lost connection to MySQL server during query')
2025-08-18 19:04:12,503 [src.langgraph.zego_tools.ocean_executor.connector] ERROR: [ocean] 创建连接池失败: (2013, 'Lost connection to MySQL server during query')
2025-08-18 19:04:12,546 [src.langgraph.zego_tools.ocean_executor.connector] ERROR: [ocean] 数据库连接失败: (2013, 'Lost connection to MySQL server during query')
2025-08-18 19:04:12,546 [src.langgraph.zego_tools.ocean_executor.connector] ERROR: [ocean] 无法获取数据库连接（异步）
2025-08-18 19:04:13,073 [src.langgraph.zego_tools.ocean_executor.connector] INFO: [ocean][sync-fallback] 查询完成，返回 29 条记录，耗时 0.65 秒

3. 深入调查这个问题，评估应该如何适配
[src.api.routes.tasks] ERROR: Task execution failed for thread_id web_39d8d5d5-aef1-4c90-ba6e-96a1304b5a4b: Type is not msgpack serializable: DAGPlan

4. 你不要管实际业务的信息，只关注前端后端开发即可


------- round3-4
1. 似乎遇到了非常复杂的序列化问题，能否使用protobuf协议解决这个问题？
2. safe_serialize函数对数据进行了截断，注意，大模型分析数据时不能使用阶段的数据，请检查这一点
3. 前端点击具体的历史任务时，出现'CheckpointTuple' object has no attribute 'get'的报错。你需要详细了解CheckpointTuple的数据类型，再进行代码调整
4. 后端数据库连接的代码非常复杂，请为期补充详细的代码注释，以便后续维护者能够继续维护
5. 底层似乎使用了连接池、fallback等方式，大大增加了复杂度，能否以你的思路来重新评估




------- 8/18 
关于DAGPlan遇到序列化失败的问题
1. DAGPlan 这个数据类型，不再保存df，让task从DataCenter获取
2. 大模型获取data_prompt的逻辑不变
3. fast-api部分，也不再传输具体数据，现在前端不需要具体数据


------- 8/19 -1

现在的模型选择逻辑需要优化
1. 大模型因token限制请求失败时，可以立刻切换模型，而不是重试3次，详见附带的日志
2. 模型等级，现在只有3级，我希望有一个重试链，而不是固定的3级，把级别从枚举改为数字，数字越小越优先
让用户这样定义,例如：
json_models = [
    {"name": "qwen-plus-latest", "weight": 1.0, "tier": 1},
    {"name": "qwen-turbo-latest", "weight": 1.0, "tier": 2},
    {"name": "qwen-12b-chat", "weight": 1.0, "tier": 3},
    {"name": "qwen-22b-chat", "weight": 1.0, "tier": 4},
]

3. 现在的模型管理非常复杂，很多字段是用不到的，需要简化，我们只需要provider、code就好了

2025-08-18 19:36:21,369 [src.langgraph.common.utils.llm_request_utils] ERROR: [f8c20aa04d588f75] ❌ 请求失败ainvoke,e=BadRequestError("Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': '<400> InternalError.Algo.InvalidParameter: Range of input length should be [1, 129024]', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-630f04c1-f599-979f-b623-8ae2bd98b558', 'request_id': '630f04c1-f599-979f-b623-8ae2bd98b558'}")
2025-08-18 19:36:22,097 [httpx] INFO: HTTP Request: POST https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions "HTTP/1.1 400 Bad Request"
2025-08-18 19:36:22,099 [src.langgraph.common.utils.llm_request_utils] ERROR: [f8c20aa04d588f75] ❌ 请求失败astream,e=BadRequestError("Error code: 400 - {'error': {'code': 'invalid_parameter_error', 'param': None, 'message': '<400> InternalError.Algo.InvalidParameter: Range of input length should be [1, 129024]', 'type': 'invalid_request_error'}, 'id': 'chatcmpl-9f8c8345-4d5f-907a-b4f6-8a8a7b570b0b', 'request_id': '9f8c8345-4d5f-907a-b4f6-8a8a7b570b0b'}")


------- 8/19-2
code review:
前端有两个同名实现不同的函数getMessageTypeLabel，这是非常不好的代码


------- 8/19-3
不要搞v1v2,这是个新项目，不要这么早留技术债包袱，没人维护v1

------- 8/19-4
1. 不要搞那个性能监控，删掉，这个项目还没发展到那个阶段，复杂度是影响项目生长的万恶之源
2. 我希望zego_cli.py也有和前端类似的相应解析逻辑，现在只有简单的print，看不出东西


------- 8/19-5
