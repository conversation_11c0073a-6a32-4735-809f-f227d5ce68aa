#!/usr/bin/env python3
"""
测试 QFlowAgent API CLI 工具
"""

import asyncio
import json

from api_cli import QFlowAgentCLI


async def test_cli_basic():
    """测试CLI基本功能"""
    print("开始测试 QFlowAgent API CLI...")
    print("=" * 50)

    # 创建CLI客户端
    cli = QFlowAgentCLI("http://localhost:2026")

    try:
        # 测试1: 健康检查
        print("1. 测试API健康检查...")
        health_result = await cli.health_check()
        print(f"   结果: {json.dumps(health_result, indent=2, ensure_ascii=False)}")
        print()

        # 测试2: 数据库连接测试
        print("2. 测试数据库连接...")
        db_result = await cli.test_db_connection()
        print(f"   结果: {json.dumps(db_result, indent=2, ensure_ascii=False)}")
        print()

        # 测试3: 获取任务列表
        print("3. 测试获取任务列表...")
        list_result = await cli.get_task_list(limit=5)
        print(f"   结果: {json.dumps(list_result, indent=2, ensure_ascii=False)}")
        print()

        # 测试4: 创建测试任务
        print("4. 测试创建任务...")
        create_result = await cli.create_task(
            user_input="测试分析任务 - 分析3s拉流请求成功率", max_workers=3, recursion_limit=50
        )
        print(f"   结果: {json.dumps(create_result, indent=2, ensure_ascii=False)}")
        print()

        # 如果有任务创建成功，测试获取详情
        if create_result.get("status") != "error":
            thread_id = create_result.get("thread_id")
            print(f"5. 测试获取任务详情 (thread_id: {thread_id})...")
            detail_result = await cli.get_task_detail(thread_id)
            print(f"   结果: {json.dumps(detail_result, indent=2, ensure_ascii=False)}")
            print()

            # 测试等待任务完成（短时间）
            print(f"6. 测试等待任务完成 (timeout: 30秒)...")
            wait_result = await cli.wait_for_task_completion(thread_id=thread_id, timeout=30, check_interval=5)
            if wait_result:
                print(f"   结果: {json.dumps(wait_result, indent=2, ensure_ascii=False)}")
            else:
                print("   任务等待超时")
            print()

        print("基本功能测试完成!")

    except Exception as e:
        print(f"测试过程中出现错误: {e}")
        import traceback

        traceback.print_exc()

    finally:
        await cli.close()


async def test_cli_stream():
    """测试CLI流式执行功能"""
    print("开始测试流式执行功能...")
    print("=" * 50)

    # 创建CLI客户端
    cli = QFlowAgentCLI("http://localhost:2026")

    try:
        # 先创建一个任务
        print("1. 创建测试任务...")
        create_result = await cli.create_task(
            user_input="测试流式执行 - 分析推流成功率", max_workers=2, recursion_limit=30
        )

        if create_result.get("status") == "error":
            print(f"创建任务失败: {create_result}")
            return

        thread_id = create_result.get("thread_id")
        print(f"任务创建成功，thread_id: {thread_id}")
        print()

        # 测试流式执行
        print("2. 开始流式执行测试...")
        print("注意: 这将启动一个真实的流式连接，按 Ctrl+C 可以中断")
        print()

        await cli.stream_task_execution(
            thread_id=thread_id, user_input="测试流式执行 - 分析推流成功率", max_workers=2, recursion_limit=30
        )

    except KeyboardInterrupt:
        print("\n流式执行被用户中断")
    except Exception as e:
        print(f"流式执行测试失败: {e}")
        import traceback

        traceback.print_exc()

    finally:
        await cli.close()


async def main():
    """主测试函数"""
    print("QFlowAgent API CLI 测试工具")
    print("=" * 60)
    print()

    # 选择测试类型
    print("请选择测试类型:")
    print("1. 基本功能测试")
    print("2. 流式执行测试")
    print("3. 全部测试")
    print()

    try:
        choice = input("请输入选择 (1/2/3): ").strip()

        if choice == "1":
            await test_cli_basic()
        elif choice == "2":
            await test_cli_stream()
        elif choice == "3":
            await test_cli_basic()
            print("\n" + "=" * 60 + "\n")
            await test_cli_stream()
        else:
            print("无效选择，执行基本功能测试")
            await test_cli_basic()

    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"测试失败: {e}")


if __name__ == "__main__":
    asyncio.run(main())
