#!/usr/bin/env python3
"""
QFlowAgent API CLI 工具
用于脱离前端调试后端API，启动分析任务等
"""

import argparse
import asyncio
import json
import os
import sys
import time
import uuid
from datetime import datetime
from typing import Any, Dict, Optional

import httpx
import sseclient

random_thread_id = f"{os.path.basename(__file__)}_{str(uuid.uuid4())}"


class QFlowAgentCLI:
    """QFlowAgent API CLI 客户端"""

    def __init__(self, base_url: str = "http://localhost:2026"):
        self.base_url = base_url
        self.client = httpx.AsyncClient(timeout=30.0)

    async def close(self):
        """关闭HTTP客户端"""
        await self.client.aclose()

    async def health_check(self) -> Dict[str, Any]:
        """检查API服务健康状态"""
        try:
            response = await self.client.get(f"{self.base_url}/health")
            response.raise_for_status()
            return response.json()
        except Exception as e:
            return {"status": "error", "message": str(e)}

    async def test_db_connection(self) -> Dict[str, Any]:
        """测试数据库连接"""
        try:
            response = await self.client.get(f"{self.base_url}/api/tasks/health/db")
            response.raise_for_status()
            return response.json()
        except Exception as e:
            return {"status": "error", "message": str(e)}

    async def create_task(self, user_input: str, max_workers: int = 5, recursion_limit: int = 100) -> Dict[str, Any]:
        """创建新任务"""
        try:
            payload = {
                "user_input": user_input,
                "max_parallel_workers": max_workers,
                "recursion_limit": recursion_limit,
            }

            response = await self.client.post(f"{self.base_url}/api/tasks/", json=payload)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            return {"status": "error", "message": str(e)}

    async def get_task_list(self, limit: int = 20, offset: int = 0) -> Dict[str, Any]:
        """获取任务列表"""
        try:
            params = {"limit": limit, "offset": offset}
            response = await self.client.get(f"{self.base_url}/api/tasks/", params=params)
            response.raise_for_status()
            return response.json()
        except Exception as e:
            return {"status": "error", "message": str(e)}

    async def get_task_detail(self, thread_id: str) -> Dict[str, Any]:
        """获取任务详情"""
        try:
            response = await self.client.get(f"{self.base_url}/api/tasks/{thread_id}")
            response.raise_for_status()
            return response.json()
        except Exception as e:
            return {"status": "error", "message": str(e)}

    async def stream_task_execution(
        self, thread_id: str, user_input: str, max_workers: int = 5, recursion_limit: int = 100
    ):
        """流式执行任务"""
        try:
            payload = {
                "user_input": user_input,
                "max_parallel_workers": max_workers,
                "recursion_limit": recursion_limit,
            }

            async with self.client.stream(
                "POST",
                f"{self.base_url}/api/tasks/{thread_id}/stream",
                json=payload,
                headers={"Accept": "text/event-stream"},
            ) as response:
                response.raise_for_status()

                print(f"开始流式执行任务 {thread_id}...")
                print("=" * 60)

                async for line in response.aiter_lines():
                    if line.startswith("data: "):
                        try:
                            data = json.loads(line[6:])  # 移除 "data: " 前缀
                            event_type = data.get("type", "unknown")
                            value = data.get("value", {})
                            timestamp = data.get("timestamp", "")

                            # 格式化输出
                            print(f"[{timestamp}] {event_type.upper()}")

                            if event_type == "updates":
                                if isinstance(value, dict):
                                    # 处理更新事件
                                    for key, val in value.items():
                                        if key == "messages":
                                            print(f"  消息: {len(val)} 条")
                                        elif key == "plans":
                                            print(f"  计划: {len(val)} 个")
                                        elif key == "worker_results":
                                            print(f"  工作结果: {len(val)} 个")
                                        else:
                                            print(f"  {key}: {val}")
                                else:
                                    print(f"  值: {value}")

                            elif event_type == "messages":
                                if isinstance(value, list) and value:
                                    latest_msg = value[-1]
                                    if isinstance(latest_msg, dict):
                                        content = latest_msg.get("content", "")
                                        role = latest_msg.get("type", "unknown")
                                        print(f"  {role}: {content[:200]}{'...' if len(content) > 200 else ''}")

                            elif event_type == "values":
                                if isinstance(value, dict):
                                    if "reporter_result" in value:
                                        result = value["reporter_result"]
                                        if result:
                                            print(f"  最终结果: {result}")
                                    else:
                                        print(f"  状态更新: {list(value.keys())}")

                            elif event_type == "error":
                                print(f"  错误: {value}")

                            elif event_type == "success":
                                print("  任务执行完成!")

                            elif event_type == "close":
                                print("  流式连接关闭")
                                break

                            print("-" * 40)

                        except json.JSONDecodeError as e:
                            print(f"JSON解析错误: {e}")
                            print(f"原始数据: {line}")
                        except Exception as e:
                            print(f"处理事件错误: {e}")

                print("=" * 60)
                print("流式执行结束")

        except Exception as e:
            print(f"流式执行失败: {e}")

    async def wait_for_task_completion(self, thread_id: str, timeout: int = 300, check_interval: int = 5):
        """等待任务完成"""
        print(f"等待任务 {thread_id} 完成...")
        start_time = time.time()

        while time.time() - start_time < timeout:
            try:
                detail = await self.get_task_detail(thread_id)
                if detail.get("status") == "completed":
                    print(f"任务 {thread_id} 已完成!")
                    return detail
                elif detail.get("status") == "failed":
                    print(f"任务 {thread_id} 失败!")
                    return detail

                print(f"任务状态: {detail.get('status', 'unknown')} - 等待 {check_interval} 秒...")
                await asyncio.sleep(check_interval)

            except Exception as e:
                print(f"检查任务状态失败: {e}")
                await asyncio.sleep(check_interval)

        print(f"等待超时 ({timeout} 秒)")
        return None


async def main():
    """主函数"""
    parser = argparse.ArgumentParser(description="QFlowAgent API CLI 工具")
    parser.add_argument("--url", default="http://localhost:2026", help="API服务地址")
    parser.add_argument(
        "--action",
        required=True,
        choices=["health", "db-test", "create", "list", "detail", "stream", "wait"],
        help="执行的操作",
    )

    # 任务相关参数
    parser.add_argument("--input", help="用户输入内容")
    parser.add_argument("--thread-id", help="任务线程ID")
    parser.add_argument("--max-workers", type=int, default=5, help="最大并行工作数")
    parser.add_argument("--recursion-limit", type=int, default=100, help="递归限制")

    # 列表和详情参数
    parser.add_argument("--limit", type=int, default=20, help="任务列表限制")
    parser.add_argument("--offset", type=int, default=0, help="任务列表偏移")

    # 等待参数
    parser.add_argument("--timeout", type=int, default=300, help="等待超时时间(秒)")
    parser.add_argument("--check-interval", type=int, default=5, help="检查间隔(秒)")

    args = parser.parse_args()

    # 创建CLI客户端
    cli = QFlowAgentCLI(args.url)

    try:
        if args.action == "health":
            # 健康检查
            result = await cli.health_check()
            print("API健康检查:")
            print(json.dumps(result, indent=2, ensure_ascii=False))

        elif args.action == "db-test":
            # 数据库连接测试
            result = await cli.test_db_connection()
            print("数据库连接测试:")
            print(json.dumps(result, indent=2, ensure_ascii=False))

        elif args.action == "create":
            # 创建任务
            if not args.input:
                print("错误: 创建任务需要提供 --input 参数")
                sys.exit(1)

            result = await cli.create_task(
                user_input=args.input, max_workers=args.max_workers, recursion_limit=args.recursion_limit
            )
            print("创建任务结果:")
            print(json.dumps(result, indent=2, ensure_ascii=False))

        elif args.action == "list":
            # 获取任务列表
            result = await cli.get_task_list(limit=args.limit, offset=args.offset)
            print("任务列表:")
            print(json.dumps(result, indent=2, ensure_ascii=False))

        elif args.action == "detail":
            # 获取任务详情
            if not args.thread_id:
                print("错误: 获取任务详情需要提供 --thread-id 参数")
                sys.exit(1)

            result = await cli.get_task_detail(args.thread_id)
            print("任务详情:")
            print(json.dumps(result, indent=2, ensure_ascii=False))

        elif args.action == "stream":
            # 流式执行任务
            if not args.thread_id:
                args.thread_id = random_thread_id
            if not args.input:
                print("错误: 流式执行任务需要提供 --input 参数")
                sys.exit(1)

            await cli.stream_task_execution(
                thread_id=args.thread_id,
                user_input=args.input,
                max_workers=args.max_workers,
                recursion_limit=args.recursion_limit,
            )

        elif args.action == "wait":
            # 等待任务完成
            if not args.thread_id:
                print("错误: 等待任务完成需要提供 --thread-id 参数")
                sys.exit(1)

            result = await cli.wait_for_task_completion(
                thread_id=args.thread_id, timeout=args.timeout, check_interval=args.check_interval
            )
            if result:
                print("任务最终状态:")
                print(json.dumps(result, indent=2, ensure_ascii=False))

    except KeyboardInterrupt:
        print("\n操作被用户中断")
    except Exception as e:
        print(f"执行失败: {e}")
        sys.exit(1)
    finally:
        await cli.close()


if __name__ == "__main__":
    # 如果没有参数，显示帮助信息
    if len(sys.argv) == 1:
        print("QFlowAgent API CLI 工具")
        print("=" * 40)
        print("用法示例:")
        print("")
        print("1. 检查API健康状态:")
        print("   uv run apps/api_cli.py --action health")
        print("")
        print("2. 测试数据库连接:")
        print("   uv run apps/api_cli.py --action db-test")
        print("")
        print("3. 创建分析任务:")
        print("   uv run apps/api_cli.py --action create --input '分析 ********** 拉流成功率'")
        print("")
        print("4. 获取任务列表:")
        print("   uv run apps/api_cli.py --action list --limit 10")
        print("")
        print("5. 获取任务详情:")
        print("   uv run apps/api_cli.py --action detail --thread-id <thread_id>")
        print("")
        print("6. 流式执行任务:")
        print("   uv run apps/api_cli.py --action stream --input '分析 ********** 拉流成功率' [--thread-id <thread_id>] ")
        print("")
        print("7. 等待任务完成:")
        print("   uv run apps/api_cli.py --action wait --thread-id <thread_id> --timeout 600")
        print("")
        print("8. 使用自定义API地址:")
        print("   uv run apps/api_cli.py --url http://localhost:8000 --action health")
        print("")
        print("完整帮助信息:")
        print("   uv run apps/api_cli.py --help")
        print("")
        sys.exit(0)

    # 运行主函数
    asyncio.run(main())
