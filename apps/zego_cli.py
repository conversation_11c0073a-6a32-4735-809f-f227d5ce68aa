import asyncio
import logging
import os
import uuid

from langchain_core.messages import AIMessage, HumanMessage

from src.langgraph.common.init_log import init_log

from src.langgraph.graph import zego_graph

init_log()


thread_id = f"{os.path.basename(__file__)}_{str(uuid.uuid4())}"
config = {
    "configurable": {
        "thread_id": thread_id,
        "max_parallel_workers": 5,
    },
    "recursion_limit": 30,
}


logger = logging.getLogger(__name__)


# values: 每个节点触发一次，并行节点合并触发一次；将state返回上来
# message: human的没有，llm请求是流式的token by token;
# updates: 返回每个节点的upate内容
async def main():
    async def stream_graph_updates(user_input: str):
        input_state = {"messages": [HumanMessage(content=user_input)]}
        async with zego_graph() as graph:

            stream_mode = ["values", "messages", "updates", "custom"]
            async for ev in graph.astream(input_state, config, stream_mode=stream_mode, debug=False):
                mode, event = ev
                print(f"\n\n### {mode} start ====================================================================\n\n")
                print(event)
                print(f"\n\n### {mode} end   ====================================================================\n\n")

    # await stream_graph_updates("""看下阿联酋 966811601 的拉流成功率近期趋势，是否存在问题，分析问题聚集性""")
    # await stream_graph_updates("""看下沙特阿拉伯 3206531758 的拉流成功率近期趋势，是否存在问题，分析问题聚集性""")
    # await stream_graph_updates("""看下沙特阿拉伯 997297939 的拉流成功率近期趋势，是否存在问题，分析问题聚集性""")
    # await stream_graph_updates("""看下 英国的拉流成功率近期趋势，是否存在问题，分析问题聚集性""")
    await stream_graph_updates("""分析下 1850816294 推流成功率下降的问题""")


asyncio.run(main())
