import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { taskApi, TaskEventSource } from '@/api'

export const useTaskStore = defineStore('task', () => {
  // 状态
  const tasks = ref([])
  const currentTask = ref(null)
  const loading = ref(false)
  const currentEventSource = ref(null)
  const streamMessages = ref([])

  // 计算属性
  const runningTasks = computed(() => 
    tasks.value.filter(task => task.status === 'running')
  )

  const completedTasks = computed(() => 
    tasks.value.filter(task => task.status === 'completed')
  )

  // 方法
  const createTask = async (userInput, options = {}) => {
    try {
      loading.value = true
      const task = await taskApi.createTask({
        user_input: userInput,
        max_parallel_workers: options.maxParallelWorkers || 5,
        recursion_limit: options.recursionLimit || 30
      })
      
      // 添加到任务列表
      tasks.value.unshift(task)
      return task
    } catch (error) {
      console.error('Failed to create task:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const loadTasks = async (params = {}) => {
    try {
      loading.value = true
      const response = await taskApi.getTasks(params)
      tasks.value = response.tasks || []
      return response
    } catch (error) {
      console.error('Failed to load tasks:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const loadTaskDetail = async (threadId) => {
    try {
      loading.value = true
      const task = await taskApi.getTaskDetail(threadId)
      currentTask.value = task
      return task
    } catch (error) {
      console.error('Failed to load task detail:', error)
      throw error
    } finally {
      loading.value = false
    }
  }

  const startTaskStream = async (threadId, userInput, options = {}) => {
    // 清理之前的连接
    if (currentEventSource.value) {
      currentEventSource.value.close()
    }

    // 清空流消息
    streamMessages.value = []

    // 创建新的EventSource连接
    const eventSource = new TaskEventSource(threadId)
    currentEventSource.value = eventSource

    // 监听不同类型的事件
    eventSource.on('values', (event) => {
      console.log('Task values:', event)
      streamMessages.value.push({
        type: 'values',
        data: event.value,
        timestamp: new Date(event.timestamp)
      })
    })

    eventSource.on('messages', (event) => {
      console.log('Task messages:', event)
      streamMessages.value.push({
        type: 'messages',
        data: event.value,
        timestamp: new Date(event.timestamp)
      })
    })

    eventSource.on('updates', (event) => {
      console.log('Task updates:', event)
      streamMessages.value.push({
        type: 'updates',
        data: event.value,
        timestamp: new Date(event.timestamp)
      })
    })

    eventSource.on('custom', (event) => {
      console.log('Task custom:', event)
      streamMessages.value.push({
        type: 'custom',
        data: event.value,
        timestamp: new Date(event.timestamp)
      })
    })

    eventSource.on('success', (event) => {
      console.log('Task completed:', event)
      streamMessages.value.push({
        type: 'success',
        data: event.value,
        timestamp: new Date(event.timestamp)
      })

      // 更新任务状态
      const taskIndex = tasks.value.findIndex(t => t.thread_id === threadId)
      if (taskIndex > -1) {
        tasks.value[taskIndex].status = 'completed'
      }
    })

    eventSource.on('error', (event) => {
      console.error('Task error:', event)
      streamMessages.value.push({
        type: 'error',
        data: event.value,
        timestamp: new Date(event.timestamp)
      })
    })

    eventSource.on('close', () => {
      console.log('EventSource closed')
      currentEventSource.value = null
    })

    // 启动任务流
    try {
      await eventSource.startTask(userInput, options)
      return eventSource
    } catch (error) {
      console.error('Failed to start task stream:', error)
      throw error
    }
  }

  const stopTaskStream = () => {
    if (currentEventSource.value) {
      currentEventSource.value.close()
      currentEventSource.value = null
    }
  }

  const updateTaskStatus = (threadId, status) => {
    const taskIndex = tasks.value.findIndex(t => t.thread_id === threadId)
    if (taskIndex > -1) {
      tasks.value[taskIndex].status = status
    }
  }

  const clearStreamMessages = () => {
    streamMessages.value = []
  }

  return {
    // 状态
    tasks,
    currentTask,
    loading,
    currentEventSource,
    streamMessages,
    
    // 计算属性
    runningTasks,
    completedTasks,
    
    // 方法
    createTask,
    loadTasks,
    loadTaskDetail,
    startTaskStream,
    stopTaskStream,
    updateTaskStatus,
    clearStreamMessages
  }
})
