import axios from 'axios'
import { ElMessage } from 'element-plus'

// 创建axios实例
const api = axios.create({
  baseURL: '/api',
  timeout: 30000,
  headers: {
    'Content-Type': 'application/json'
  }
})

// 请求拦截器
api.interceptors.request.use(
  config => {
    return config
  },
  error => {
    return Promise.reject(error)
  }
)

// 响应拦截器
api.interceptors.response.use(
  response => {
    return response.data
  },
  error => {
    const message = error.response?.data?.detail || error.message || '请求失败'
    ElMessage.error(message)
    return Promise.reject(error)
  }
)

// API方法
export const taskApi = {
  // 创建任务
  createTask(data) {
    return api.post('/tasks/', data)
  },

  // 获取任务列表
  getTasks(params = {}) {
    return api.get('/tasks/', { params })
  },

  // 获取任务详情
  getTaskDetail(threadId) {
    return api.get(`/tasks/${threadId}`)
  }
}

// Server-Sent Events 连接类
export class TaskEventSource {
  constructor(threadId) {
    this.threadId = threadId
    this.eventSource = null
    this.listeners = {
      values: [],
      messages: [],
      updates: [],
      custom: [],
      success: [],
      error: [],
      close: []
    }
  }

  startTask(userInput, options = {}) {
    const url = `/api/tasks/${this.threadId}/stream`

    // 使用POST请求启动流式任务
    return fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Accept': 'text/event-stream'
      },
      body: JSON.stringify({
        user_input: userInput,
        max_parallel_workers: options.maxParallelWorkers || 5,
        recursion_limit: options.recursionLimit || 30
      })
    }).then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error! status: ${response.status}`)
      }

      // 立即发送开始事件
      this.emit('start', { thread_id: this.threadId, user_input: userInput })

      // 创建EventSource来读取流式响应
      this.createEventSourceFromResponse(response)
      return response
    })
  }

  createEventSourceFromResponse(response) {
    const reader = response.body.getReader()
    const decoder = new TextDecoder()
    let buffer = ''

    const readStream = async () => {
      try {
        while (true) {
          const { done, value } = await reader.read()

          if (done) {
            this.emit('close')
            break
          }

          // 解码数据并添加到缓冲区
          buffer += decoder.decode(value, { stream: true })

          // 按行处理数据
          const lines = buffer.split('\n')
          buffer = lines.pop() || '' // 保留最后一个不完整的行

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              try {
                const data = line.slice(6) // 移除 'data: ' 前缀
                if (data.trim()) {
                  const event = JSON.parse(data)
                  console.log('Received SSE event:', event)
                  this.emit(event.type, event)
                }
              } catch (error) {
                console.error('Failed to parse SSE data:', error, 'Raw data:', line)
              }
            }
          }
        }
      } catch (error) {
        console.error('Stream reading error:', error)
        this.emit('error', { error: error.message })
      }
    }

    readStream()
  }

  on(event, callback) {
    if (this.listeners[event]) {
      this.listeners[event].push(callback)
    }
  }

  off(event, callback) {
    if (this.listeners[event]) {
      const index = this.listeners[event].indexOf(callback)
      if (index > -1) {
        this.listeners[event].splice(index, 1)
      }
    }
  }

  emit(event, data) {
    if (this.listeners[event]) {
      this.listeners[event].forEach(callback => callback(data))
    }
  }

  close() {
    if (this.eventSource) {
      this.eventSource.close()
      this.eventSource = null
    }
  }
}

export default api
