<template>
  <div class="task-center">
    <!-- 创建任务区域 -->
    <el-card class="create-task-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <el-icon><Plus /></el-icon>
          <span>创建新任务</span>
        </div>
      </template>
      
      <el-form @submit.prevent="handleCreateTask">
        <el-form-item label="查询内容">
          <el-input
            v-model="userInput"
            type="textarea"
            :rows="4"
            placeholder="请输入您的查询内容，例如：分析下 1850816294 推流成功率下降的问题"
            maxlength="1000"
            show-word-limit
          />
        </el-form-item>
        
        <el-form-item>
          <el-row :gutter="16">
            <el-col :span="12">
              <el-form-item label="最大并行数">
                <el-input-number
                  v-model="maxParallelWorkers"
                  :min="1"
                  :max="20"
                  controls-position="right"
                />
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="递归限制">
                <el-input-number
                  v-model="recursionLimit"
                  :min="10"
                  :max="500"
                  controls-position="right"
                />
              </el-form-item>
            </el-col>
          </el-row>
        </el-form-item>
        
        <el-form-item>
          <el-button 
            type="primary" 
            @click="handleCreateTask"
            :loading="taskStore.loading"
            :disabled="!userInput.trim()"
            size="large"
          >
            <el-icon><VideoPlay /></el-icon>
            启动任务
          </el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <!-- 实时任务流 -->
    <el-card v-if="currentTaskId" class="stream-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <el-icon><Monitor /></el-icon>
          <span>任务执行中 - {{ currentTaskId }}</span>
          <el-button 
            type="danger" 
            size="small" 
            @click="handleStopTask"
            style="margin-left: auto;"
          >
            停止任务
          </el-button>
        </div>
      </template>
      
      <div class="stream-container">
        <div 
          v-for="(message, index) in taskStore.streamMessages" 
          :key="index"
          class="stream-message"
          :class="`message-${message.type}`"
        >
          <div class="message-header">
            <el-tag 
              :type="getMessageTagType(message.type)"
              size="small"
            >
              {{ getMessageTypeLabel(message.type) }}
            </el-tag>
            <span class="message-time">
              {{ formatTime(message.timestamp) }}
            </span>
          </div>
          
          <div class="message-content">
            <template v-if="message.type === 'messages'">
              <div class="messages-content">
                <div v-for="(msg, msgIndex) in (Array.isArray(message.data) ? message.data : [message.data])" :key="msgIndex" class="single-message">
                  <strong v-if="msg.name">{{ msg.name }}:</strong>
                  <div class="message-text">{{ msg.content || JSON.stringify(msg) }}</div>
                </div>
              </div>
            </template>
            <template v-else-if="message.type === 'error'">
              <div class="error-message">
                {{ typeof message.data === 'string' ? message.data : JSON.stringify(message.data) }}
              </div>
            </template>
            <template v-else-if="message.type === 'success'">
              <div class="success-message">
                任务执行完成
              </div>
            </template>
            <template v-else>
              <pre class="json-content">{{ JSON.stringify(message.data, null, 2) }}</pre>
            </template>
          </div>
        </div>
        
        <div v-if="taskStore.streamMessages.length === 0" class="empty-stream">
          <el-icon><Loading /></el-icon>
          <span>等待任务开始...</span>
        </div>
      </div>
    </el-card>

    <!-- 最近任务 -->
    <el-card class="recent-tasks-card" shadow="hover">
      <template #header>
        <div class="card-header">
          <el-icon><Clock /></el-icon>
          <span>最近任务</span>
          <el-button 
            type="primary" 
            size="small" 
            @click="$router.push('/history')"
            style="margin-left: auto;"
          >
            查看全部
          </el-button>
        </div>
      </template>
      
      <el-table 
        :data="taskStore.tasks.slice(0, 5)" 
        style="width: 100%"
        v-loading="taskStore.loading"
      >
        <el-table-column prop="thread_id" label="任务ID" width="200">
          <template #default="{ row }">
            <el-link 
              type="primary" 
              @click="$router.push(`/task/${row.thread_id}`)"
            >
              {{ row.thread_id.slice(-8) }}
            </el-link>
          </template>
        </el-table-column>
        
        <el-table-column prop="user_input" label="查询内容" show-overflow-tooltip>
          <template #default="{ row }">
            {{ row.user_input.slice(0, 50) }}{{ row.user_input.length > 50 ? '...' : '' }}
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.created_at) }}
          </template>
        </el-table-column>
      </el-table>
    </el-card>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { ElMessage } from 'element-plus'
import { Plus, VideoPlay, Monitor, Clock, Loading } from '@element-plus/icons-vue'
import { useTaskStore } from '@/stores/task'
import dayjs from 'dayjs'
import {
  getStreamMessageTypeLabel,
  getStreamMessageTagType,
  getTaskStatusLabel,
  getTaskStatusTagType,
  formatTime
} from '@/utils/messageUtils'

const router = useRouter()
const taskStore = useTaskStore()

// 表单数据
const userInput = ref('')
const maxParallelWorkers = ref(5)
const recursionLimit = ref(30)
const currentTaskId = ref(null)

// 方法
const handleCreateTask = async () => {
  if (!userInput.value.trim()) {
    ElMessage.warning('请输入查询内容')
    return
  }

  try {
    const task = await taskStore.createTask(userInput.value, {
      maxParallelWorkers: maxParallelWorkers.value,
      recursionLimit: recursionLimit.value
    })

    ElMessage.success('任务创建成功')
    
    // 启动实时流
    currentTaskId.value = task.thread_id
    await taskStore.startTaskStream(task.thread_id, userInput.value, {
      maxParallelWorkers: maxParallelWorkers.value,
      recursionLimit: recursionLimit.value
    })

    // 清空表单
    userInput.value = ''
  } catch (error) {
    ElMessage.error('任务创建失败')
  }
}

const handleStopTask = () => {
  taskStore.stopTaskStream()
  currentTaskId.value = null
  ElMessage.info('任务已停止')
}

// 使用统一的工具函数替代重复的本地函数
const getMessageTagType = getStreamMessageTagType
const getMessageTypeLabel = getStreamMessageTypeLabel
const getStatusTagType = getTaskStatusTagType
const getStatusLabel = getTaskStatusLabel

// 使用统一的时间格式化函数，但保留本地的简单格式化
const formatDateTime = (time) => {
  return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
}

// 生命周期
onMounted(() => {
  taskStore.loadTasks({ limit: 10 })
})

onUnmounted(() => {
  taskStore.stopTaskStream()
})
</script>

<style scoped>
.task-center {
  max-width: 1200px;
  margin: 0 auto;
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: bold;
}

.create-task-card {
  margin-bottom: 20px;
}

.stream-card {
  margin-bottom: 20px;
}

.stream-container {
  max-height: 500px;
  overflow-y: auto;
  border: 1px solid var(--el-border-color);
  border-radius: 4px;
  padding: 16px;
  background: var(--el-bg-color-page);
}

.stream-message {
  margin-bottom: 16px;
  padding: 12px;
  border-radius: 6px;
  border-left: 4px solid var(--el-color-primary);
  background: var(--el-fill-color-lighter);
}

.message-ai_message {
  border-left-color: var(--el-color-primary);
}

.message-error {
  border-left-color: var(--el-color-danger);
  background: var(--el-color-danger-light-9);
}

.message-complete {
  border-left-color: var(--el-color-success);
  background: var(--el-color-success-light-9);
}

.message-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.message-time {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.message-content {
  font-size: 14px;
}

.messages-content {
  line-height: 1.6;
}

.single-message {
  margin-bottom: 8px;
  padding: 8px;
  background: var(--el-bg-color);
  border-radius: 4px;
}

.message-text {
  margin-top: 4px;
  white-space: pre-wrap;
}

.error-message {
  color: var(--el-color-danger);
  font-weight: bold;
}

.success-message {
  color: var(--el-color-success);
  font-weight: bold;
}

.json-content {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  background: var(--el-bg-color);
  padding: 8px;
  border-radius: 4px;
  overflow-x: auto;
}

.empty-stream {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  color: var(--el-text-color-secondary);
  font-size: 14px;
  padding: 40px;
}

.recent-tasks-card {
  flex: 1;
}
</style>
