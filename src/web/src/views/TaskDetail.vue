<template>
  <div class="task-detail">
    <el-card v-if="taskStore.currentTask" shadow="hover">
      <template #header>
        <div class="card-header">
          <el-button 
            type="primary" 
            size="small" 
            @click="$router.back()"
          >
            <el-icon><ArrowLeft /></el-icon>
            返回
          </el-button>
          <span>任务详情 - {{ taskStore.currentTask.thread_id.slice(-12) }}</span>
          <el-tag :type="getStatusTagType(taskStore.currentTask.status)">
            {{ getStatusLabel(taskStore.currentTask.status) }}
          </el-tag>
        </div>
      </template>

      <!-- 基本信息 -->
      <div class="task-info">
        <el-descriptions :column="2" border>
          <el-descriptions-item label="任务ID">
            {{ taskStore.currentTask.thread_id }}
          </el-descriptions-item>
          <el-descriptions-item label="状态">
            <el-tag :type="getStatusTagType(taskStore.currentTask.status)">
              {{ getStatusLabel(taskStore.currentTask.status) }}
            </el-tag>
          </el-descriptions-item>
          <el-descriptions-item label="创建时间">
            {{ formatDateTime(taskStore.currentTask.created_at) }}
          </el-descriptions-item>
          <el-descriptions-item label="更新时间">
            {{ taskStore.currentTask.updated_at ? formatDateTime(taskStore.currentTask.updated_at) : '-' }}
          </el-descriptions-item>
          <el-descriptions-item label="查询内容" :span="2">
            <div class="user-input">
              {{ taskStore.currentTask.user_input }}
            </div>
          </el-descriptions-item>
        </el-descriptions>
      </div>

      <!-- 配置信息 -->
      <el-divider content-position="left">配置信息</el-divider>
      <div class="config-info">
        <pre class="json-display">{{ JSON.stringify(taskStore.currentTask.config, null, 2) }}</pre>
      </div>

      <!-- 消息历史 -->
      <el-divider content-position="left">消息历史</el-divider>
      <div class="messages-section">
        <div 
          v-for="(message, index) in taskStore.currentTask.messages" 
          :key="index"
          class="message-item"
          :class="`message-${message.type}`"
        >
          <div class="message-header">
            <el-tag 
              :type="getMessageTagType(message.type)"
              size="small"
            >
              {{ getMessageTypeLabel(message.type) }}
            </el-tag>
            <span v-if="message.name" class="message-name">{{ message.name }}</span>
          </div>
          <div class="message-content">
            {{ message.content }}
          </div>
        </div>
        
        <div v-if="taskStore.currentTask.messages.length === 0" class="empty-messages">
          <el-empty description="暂无消息历史" />
        </div>
      </div>

      <!-- Checkpoint历史 -->
      <el-divider content-position="left">执行历史</el-divider>
      <div class="checkpoints-section">
        <el-timeline>
          <el-timeline-item
            v-for="(checkpoint, index) in taskStore.currentTask.checkpoints"
            :key="index"
            :timestamp="formatDateTime(checkpoint.ts)"
            placement="top"
          >
            <el-card shadow="hover" class="checkpoint-card">
              <div class="checkpoint-header">
                <span>步骤 {{ checkpoint.step }}</span>
                <el-tag v-if="checkpoint.metadata" size="small">
                  {{ Object.keys(checkpoint.metadata).length }} 个元数据
                </el-tag>
              </div>
              
              <div v-if="checkpoint.writes && Object.keys(checkpoint.writes).length > 0" class="checkpoint-writes">
                <strong>写入操作:</strong>
                <pre class="json-display">{{ JSON.stringify(checkpoint.writes, null, 2) }}</pre>
              </div>
              
              <div v-if="checkpoint.metadata && Object.keys(checkpoint.metadata).length > 0" class="checkpoint-metadata">
                <strong>元数据:</strong>
                <pre class="json-display">{{ JSON.stringify(checkpoint.metadata, null, 2) }}</pre>
              </div>
            </el-card>
          </el-timeline-item>
        </el-timeline>
        
        <div v-if="taskStore.currentTask.checkpoints.length === 0" class="empty-checkpoints">
          <el-empty description="暂无执行历史" />
        </div>
      </div>
    </el-card>

    <div v-else class="loading-container">
      <el-skeleton :rows="10" animated />
    </div>
  </div>
</template>

<script setup>
import { onMounted } from 'vue'
import { useRoute } from 'vue-router'
import { ArrowLeft } from '@element-plus/icons-vue'
import { useTaskStore } from '@/stores/task'
import dayjs from 'dayjs'
import {
  getChatMessageTypeLabel,
  getChatMessageTagType,
  getTaskStatusLabel,
  getTaskStatusTagType
} from '@/utils/messageUtils'

const route = useRoute()
const taskStore = useTaskStore()

const threadId = route.params.threadId

// 使用统一的工具函数替代重复的本地函数
const getStatusTagType = getTaskStatusTagType
const getStatusLabel = getTaskStatusLabel
const getMessageTagType = getChatMessageTagType
const getMessageTypeLabel = getChatMessageTypeLabel

const formatDateTime = (time) => {
  return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
}

// 生命周期
onMounted(() => {
  if (threadId) {
    taskStore.loadTaskDetail(threadId)
  }
})
</script>

<style scoped>
.task-detail {
  max-width: 1200px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 12px;
  font-weight: bold;
}

.task-info {
  margin-bottom: 20px;
}

.user-input {
  padding: 12px;
  background: var(--el-fill-color-lighter);
  border-radius: 4px;
  white-space: pre-wrap;
  line-height: 1.6;
}

.config-info {
  margin-bottom: 20px;
}

.json-display {
  background: var(--el-fill-color-lighter);
  padding: 12px;
  border-radius: 4px;
  font-size: 12px;
  color: var(--el-text-color-secondary);
  overflow-x: auto;
  max-height: 300px;
  overflow-y: auto;
}

.messages-section {
  margin-bottom: 20px;
}

.message-item {
  margin-bottom: 16px;
  padding: 12px;
  border-radius: 6px;
  border-left: 4px solid var(--el-color-primary);
  background: var(--el-fill-color-lighter);
}

.message-human {
  border-left-color: var(--el-color-primary);
}

.message-ai {
  border-left-color: var(--el-color-success);
}

.message-system {
  border-left-color: var(--el-color-info);
}

.message-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.message-name {
  font-weight: bold;
  color: var(--el-text-color-primary);
}

.message-content {
  white-space: pre-wrap;
  line-height: 1.6;
}

.checkpoints-section {
  margin-bottom: 20px;
}

.checkpoint-card {
  margin-bottom: 8px;
}

.checkpoint-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
  font-weight: bold;
}

.checkpoint-writes,
.checkpoint-metadata {
  margin-top: 12px;
}

.empty-messages,
.empty-checkpoints {
  text-align: center;
  padding: 40px;
}

.loading-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}
</style>
