<template>
  <div class="task-history">
    <el-card shadow="hover">
      <template #header>
        <div class="card-header">
          <el-icon><Document /></el-icon>
          <span>任务历史</span>
          <el-button 
            type="primary" 
            size="small" 
            @click="handleRefresh"
            :loading="taskStore.loading"
          >
            刷新
          </el-button>
        </div>
      </template>

      <!-- 筛选器 -->
      <div class="filters">
        <el-row :gutter="16">
          <el-col :span="6">
            <el-select v-model="statusFilter" placeholder="状态筛选" clearable>
              <el-option label="全部" value="" />
              <el-option label="等待中" value="pending" />
              <el-option label="运行中" value="running" />
              <el-option label="已完成" value="completed" />
              <el-option label="失败" value="failed" />
            </el-select>
          </el-col>
          <el-col :span="8">
            <el-input
              v-model="searchKeyword"
              placeholder="搜索任务内容"
              clearable
              @keyup.enter="handleSearch"
            >
              <template #append>
                <el-button @click="handleSearch">
                  <el-icon><Search /></el-icon>
                </el-button>
              </template>
            </el-input>
          </el-col>
        </el-row>
      </div>

      <!-- 任务表格 -->
      <el-table 
        :data="filteredTasks" 
        style="width: 100%"
        v-loading="taskStore.loading"
        @row-click="handleRowClick"
        row-style="cursor: pointer;"
      >
        <el-table-column prop="thread_id" label="任务ID" width="200">
          <template #default="{ row }">
            <el-link 
              type="primary" 
              @click.stop="$router.push(`/task/${row.thread_id}`)"
            >
              {{ row.thread_id.slice(-12) }}
            </el-link>
          </template>
        </el-table-column>
        
        <el-table-column prop="user_input" label="查询内容" show-overflow-tooltip>
          <template #default="{ row }">
            <div class="task-input">
              {{ row.user_input }}
            </div>
          </template>
        </el-table-column>
        
        <el-table-column prop="status" label="状态" width="100">
          <template #default="{ row }">
            <el-tag :type="getStatusTagType(row.status)">
              {{ getStatusLabel(row.status) }}
            </el-tag>
          </template>
        </el-table-column>
        
        <el-table-column prop="created_at" label="创建时间" width="180">
          <template #default="{ row }">
            {{ formatDateTime(row.created_at) }}
          </template>
        </el-table-column>

        <el-table-column label="操作" width="120">
          <template #default="{ row }">
            <el-button 
              type="primary" 
              size="small" 
              @click.stop="$router.push(`/task/${row.thread_id}`)"
            >
              查看详情
            </el-button>
          </template>
        </el-table-column>
      </el-table>

      <!-- 分页 -->
      <div class="pagination">
        <el-pagination
          v-model:current-page="currentPage"
          v-model:page-size="pageSize"
          :page-sizes="[10, 20, 50, 100]"
          :total="totalTasks"
          layout="total, sizes, prev, pager, next, jumper"
          @size-change="handleSizeChange"
          @current-change="handleCurrentChange"
        />
      </div>
    </el-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { Document, Search } from '@element-plus/icons-vue'
import { useTaskStore } from '@/stores/task'
import dayjs from 'dayjs'
import { getTaskStatusLabel, getTaskStatusTagType } from '@/utils/messageUtils'

const router = useRouter()
const taskStore = useTaskStore()

// 筛选和搜索
const statusFilter = ref('')
const searchKeyword = ref('')
const currentPage = ref(1)
const pageSize = ref(20)
const totalTasks = ref(0)

// 计算属性
const filteredTasks = computed(() => {
  let tasks = taskStore.tasks

  // 状态筛选
  if (statusFilter.value) {
    tasks = tasks.filter(task => task.status === statusFilter.value)
  }

  // 关键词搜索
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    tasks = tasks.filter(task => 
      task.user_input.toLowerCase().includes(keyword) ||
      task.thread_id.toLowerCase().includes(keyword)
    )
  }

  totalTasks.value = tasks.length
  return tasks
})

// 方法
const handleRefresh = () => {
  loadTasks()
}

const handleSearch = () => {
  currentPage.value = 1
  // 筛选逻辑在计算属性中处理
}

const handleRowClick = (row) => {
  router.push(`/task/${row.thread_id}`)
}

const handleSizeChange = (size) => {
  pageSize.value = size
  currentPage.value = 1
  loadTasks()
}

const handleCurrentChange = (page) => {
  currentPage.value = page
  loadTasks()
}

const loadTasks = () => {
  const offset = (currentPage.value - 1) * pageSize.value
  taskStore.loadTasks({
    limit: pageSize.value,
    offset: offset
  })
}

// 使用统一的工具函数替代重复的本地函数
const getStatusTagType = getTaskStatusTagType
const getStatusLabel = getTaskStatusLabel

const formatDateTime = (time) => {
  return dayjs(time).format('YYYY-MM-DD HH:mm:ss')
}

// 监听筛选条件变化
watch([statusFilter, searchKeyword], () => {
  currentPage.value = 1
})

// 生命周期
onMounted(() => {
  loadTasks()
})
</script>

<style scoped>
.task-history {
  max-width: 1200px;
  margin: 0 auto;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  font-weight: bold;
}

.filters {
  margin-bottom: 20px;
}

.task-input {
  max-width: 300px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.pagination {
  margin-top: 20px;
  display: flex;
  justify-content: center;
}
</style>
