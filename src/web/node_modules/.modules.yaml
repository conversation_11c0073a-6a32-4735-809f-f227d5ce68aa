hoistPattern:
  - '*'
hoistedDependencies:
  '@babel/helper-string-parser@7.27.1':
    '@babel/helper-string-parser': private
  '@babel/helper-validator-identifier@7.27.1':
    '@babel/helper-validator-identifier': private
  '@babel/parser@7.28.3':
    '@babel/parser': private
  '@babel/types@7.28.2':
    '@babel/types': private
  '@ctrl/tinycolor@3.6.1':
    '@ctrl/tinycolor': private
  '@esbuild/darwin-arm64@0.21.5':
    '@esbuild/darwin-arm64': private
  '@eslint-community/eslint-utils@4.7.0(eslint@8.57.1)':
    '@eslint-community/eslint-utils': private
  '@eslint-community/regexpp@4.12.1':
    '@eslint-community/regexpp': private
  '@eslint/eslintrc@2.1.4':
    '@eslint/eslintrc': private
  '@eslint/js@8.57.1':
    '@eslint/js': private
  '@floating-ui/core@1.7.3':
    '@floating-ui/core': private
  '@floating-ui/dom@1.7.3':
    '@floating-ui/dom': private
  '@floating-ui/utils@0.2.10':
    '@floating-ui/utils': private
  '@humanwhocodes/config-array@0.13.0':
    '@humanwhocodes/config-array': private
  '@humanwhocodes/module-importer@1.0.1':
    '@humanwhocodes/module-importer': private
  '@humanwhocodes/object-schema@2.0.3':
    '@humanwhocodes/object-schema': private
  '@jridgewell/sourcemap-codec@1.5.5':
    '@jridgewell/sourcemap-codec': private
  '@nodelib/fs.scandir@2.1.5':
    '@nodelib/fs.scandir': private
  '@nodelib/fs.stat@2.0.5':
    '@nodelib/fs.stat': private
  '@nodelib/fs.walk@1.2.8':
    '@nodelib/fs.walk': private
  '@pkgr/core@0.2.9':
    '@pkgr/core': private
  '@rollup/rollup-darwin-arm64@4.46.3':
    '@rollup/rollup-darwin-arm64': private
  '@sxzz/popperjs-es@2.11.7':
    '@popperjs/core': private
  '@types/estree@1.0.8':
    '@types/estree': private
  '@types/lodash-es@4.17.12':
    '@types/lodash-es': private
  '@types/lodash@4.17.20':
    '@types/lodash': private
  '@types/web-bluetooth@0.0.16':
    '@types/web-bluetooth': private
  '@ungap/structured-clone@1.3.0':
    '@ungap/structured-clone': private
  '@vue/compiler-core@3.5.18':
    '@vue/compiler-core': private
  '@vue/compiler-dom@3.5.18':
    '@vue/compiler-dom': private
  '@vue/compiler-sfc@3.5.18':
    '@vue/compiler-sfc': private
  '@vue/compiler-ssr@3.5.18':
    '@vue/compiler-ssr': private
  '@vue/devtools-api@6.6.4':
    '@vue/devtools-api': private
  '@vue/reactivity@3.5.18':
    '@vue/reactivity': private
  '@vue/runtime-core@3.5.18':
    '@vue/runtime-core': private
  '@vue/runtime-dom@3.5.18':
    '@vue/runtime-dom': private
  '@vue/server-renderer@3.5.18(vue@3.5.18)':
    '@vue/server-renderer': private
  '@vue/shared@3.5.18':
    '@vue/shared': private
  '@vueuse/core@9.13.0(vue@3.5.18)':
    '@vueuse/core': private
  '@vueuse/metadata@9.13.0':
    '@vueuse/metadata': private
  '@vueuse/shared@9.13.0(vue@3.5.18)':
    '@vueuse/shared': private
  acorn-jsx@5.3.2(acorn@8.15.0):
    acorn-jsx: private
  acorn@8.15.0:
    acorn: private
  ajv@6.12.6:
    ajv: private
  ansi-regex@5.0.1:
    ansi-regex: private
  ansi-styles@4.3.0:
    ansi-styles: private
  argparse@2.0.1:
    argparse: private
  async-validator@4.2.5:
    async-validator: private
  asynckit@0.4.0:
    asynckit: private
  balanced-match@1.0.2:
    balanced-match: private
  boolbase@1.0.0:
    boolbase: private
  brace-expansion@1.1.12:
    brace-expansion: private
  call-bind-apply-helpers@1.0.2:
    call-bind-apply-helpers: private
  callsites@3.1.0:
    callsites: private
  chalk@4.1.2:
    chalk: private
  color-convert@2.0.1:
    color-convert: private
  color-name@1.1.4:
    color-name: private
  combined-stream@1.0.8:
    combined-stream: private
  concat-map@0.0.1:
    concat-map: private
  cross-spawn@7.0.6:
    cross-spawn: private
  cssesc@3.0.0:
    cssesc: private
  csstype@3.1.3:
    csstype: private
  debug@4.4.1:
    debug: private
  deep-is@0.1.4:
    deep-is: private
  delayed-stream@1.0.0:
    delayed-stream: private
  doctrine@3.0.0:
    doctrine: private
  dunder-proto@1.0.1:
    dunder-proto: private
  entities@4.5.0:
    entities: private
  es-define-property@1.0.1:
    es-define-property: private
  es-errors@1.3.0:
    es-errors: private
  es-object-atoms@1.1.1:
    es-object-atoms: private
  es-set-tostringtag@2.1.0:
    es-set-tostringtag: private
  esbuild@0.21.5:
    esbuild: private
  escape-html@1.0.3:
    escape-html: private
  escape-string-regexp@4.0.0:
    escape-string-regexp: private
  eslint-config-prettier@9.1.2(eslint@8.57.1):
    eslint-config-prettier: private
  eslint-plugin-prettier@5.5.4(eslint-config-prettier@9.1.2(eslint@8.57.1))(eslint@8.57.1)(prettier@3.6.2):
    eslint-plugin-prettier: private
  eslint-scope@7.2.2:
    eslint-scope: private
  eslint-visitor-keys@3.4.3:
    eslint-visitor-keys: private
  espree@9.6.1:
    espree: private
  esquery@1.6.0:
    esquery: private
  esrecurse@4.3.0:
    esrecurse: private
  estraverse@5.3.0:
    estraverse: private
  estree-walker@2.0.2:
    estree-walker: private
  esutils@2.0.3:
    esutils: private
  fast-deep-equal@3.1.3:
    fast-deep-equal: private
  fast-diff@1.3.0:
    fast-diff: private
  fast-json-stable-stringify@2.1.0:
    fast-json-stable-stringify: private
  fast-levenshtein@2.0.6:
    fast-levenshtein: private
  fastq@1.19.1:
    fastq: private
  file-entry-cache@6.0.1:
    file-entry-cache: private
  find-up@5.0.0:
    find-up: private
  flat-cache@3.2.0:
    flat-cache: private
  flatted@3.3.3:
    flatted: private
  follow-redirects@1.15.11:
    follow-redirects: private
  form-data@4.0.4:
    form-data: private
  fs.realpath@1.0.0:
    fs.realpath: private
  fsevents@2.3.3:
    fsevents: private
  function-bind@1.1.2:
    function-bind: private
  get-intrinsic@1.3.0:
    get-intrinsic: private
  get-proto@1.0.1:
    get-proto: private
  glob-parent@6.0.2:
    glob-parent: private
  glob@7.2.3:
    glob: private
  globals@13.24.0:
    globals: private
  gopd@1.2.0:
    gopd: private
  graphemer@1.4.0:
    graphemer: private
  has-flag@4.0.0:
    has-flag: private
  has-symbols@1.1.0:
    has-symbols: private
  has-tostringtag@1.0.2:
    has-tostringtag: private
  hasown@2.0.2:
    hasown: private
  ignore@5.3.2:
    ignore: private
  import-fresh@3.3.1:
    import-fresh: private
  imurmurhash@0.1.4:
    imurmurhash: private
  inflight@1.0.6:
    inflight: private
  inherits@2.0.4:
    inherits: private
  is-extglob@2.1.1:
    is-extglob: private
  is-glob@4.0.3:
    is-glob: private
  is-path-inside@3.0.3:
    is-path-inside: private
  isexe@2.0.0:
    isexe: private
  js-yaml@4.1.0:
    js-yaml: private
  json-buffer@3.0.1:
    json-buffer: private
  json-schema-traverse@0.4.1:
    json-schema-traverse: private
  json-stable-stringify-without-jsonify@1.0.1:
    json-stable-stringify-without-jsonify: private
  keyv@4.5.4:
    keyv: private
  levn@0.4.1:
    levn: private
  locate-path@6.0.0:
    locate-path: private
  lodash-es@4.17.21:
    lodash-es: private
  lodash-unified@1.0.3(@types/lodash-es@4.17.12)(lodash-es@4.17.21)(lodash@4.17.21):
    lodash-unified: private
  lodash.merge@4.6.2:
    lodash.merge: private
  lodash@4.17.21:
    lodash: private
  magic-string@0.30.17:
    magic-string: private
  math-intrinsics@1.1.0:
    math-intrinsics: private
  memoize-one@6.0.0:
    memoize-one: private
  mime-db@1.52.0:
    mime-db: private
  mime-types@2.1.35:
    mime-types: private
  minimatch@3.1.2:
    minimatch: private
  ms@2.1.3:
    ms: private
  nanoid@3.3.11:
    nanoid: private
  natural-compare@1.4.0:
    natural-compare: private
  normalize-wheel-es@1.2.0:
    normalize-wheel-es: private
  nth-check@2.1.1:
    nth-check: private
  once@1.4.0:
    once: private
  optionator@0.9.4:
    optionator: private
  p-limit@3.1.0:
    p-limit: private
  p-locate@5.0.0:
    p-locate: private
  parent-module@1.0.1:
    parent-module: private
  path-exists@4.0.0:
    path-exists: private
  path-is-absolute@1.0.1:
    path-is-absolute: private
  path-key@3.1.1:
    path-key: private
  picocolors@1.1.1:
    picocolors: private
  postcss-selector-parser@6.1.2:
    postcss-selector-parser: private
  postcss@8.5.6:
    postcss: private
  prelude-ls@1.2.1:
    prelude-ls: private
  prettier-linter-helpers@1.0.0:
    prettier-linter-helpers: private
  proxy-from-env@1.1.0:
    proxy-from-env: private
  punycode@2.3.1:
    punycode: private
  queue-microtask@1.2.3:
    queue-microtask: private
  resolve-from@4.0.0:
    resolve-from: private
  reusify@1.1.0:
    reusify: private
  rimraf@3.0.2:
    rimraf: private
  rollup@4.46.3:
    rollup: private
  run-parallel@1.2.0:
    run-parallel: private
  semver@7.7.2:
    semver: private
  shebang-command@2.0.0:
    shebang-command: private
  shebang-regex@3.0.0:
    shebang-regex: private
  source-map-js@1.2.1:
    source-map-js: private
  strip-ansi@6.0.1:
    strip-ansi: private
  strip-json-comments@3.1.1:
    strip-json-comments: private
  supports-color@7.2.0:
    supports-color: private
  synckit@0.11.11:
    synckit: private
  text-table@0.2.0:
    text-table: private
  type-check@0.4.0:
    type-check: private
  type-fest@0.20.2:
    type-fest: private
  uri-js@4.4.1:
    uri-js: private
  util-deprecate@1.0.2:
    util-deprecate: private
  vue-demi@0.14.10(vue@3.5.18):
    vue-demi: private
  vue-eslint-parser@9.4.3(eslint@8.57.1):
    vue-eslint-parser: private
  which@2.0.2:
    which: private
  word-wrap@1.2.5:
    word-wrap: private
  wrappy@1.0.2:
    wrappy: private
  xml-name-validator@4.0.0:
    xml-name-validator: private
  yocto-queue@0.1.0:
    yocto-queue: private
ignoredBuilds:
  - esbuild
  - vue-demi
included:
  dependencies: true
  devDependencies: true
  optionalDependencies: true
injectedDeps: {}
layoutVersion: 5
nodeLinker: isolated
packageManager: pnpm@10.14.0
pendingBuilds: []
prunedAt: Mon, 18 Aug 2025 10:22:51 GMT
publicHoistPattern: []
registries:
  '@jsr': https://npm.jsr.io/
  default: http://mirrors.cloud.tencent.com/npm/
skipped:
  - '@esbuild/aix-ppc64@0.21.5'
  - '@esbuild/android-arm64@0.21.5'
  - '@esbuild/android-arm@0.21.5'
  - '@esbuild/android-x64@0.21.5'
  - '@esbuild/darwin-x64@0.21.5'
  - '@esbuild/freebsd-arm64@0.21.5'
  - '@esbuild/freebsd-x64@0.21.5'
  - '@esbuild/linux-arm64@0.21.5'
  - '@esbuild/linux-arm@0.21.5'
  - '@esbuild/linux-ia32@0.21.5'
  - '@esbuild/linux-loong64@0.21.5'
  - '@esbuild/linux-mips64el@0.21.5'
  - '@esbuild/linux-ppc64@0.21.5'
  - '@esbuild/linux-riscv64@0.21.5'
  - '@esbuild/linux-s390x@0.21.5'
  - '@esbuild/linux-x64@0.21.5'
  - '@esbuild/netbsd-x64@0.21.5'
  - '@esbuild/openbsd-x64@0.21.5'
  - '@esbuild/sunos-x64@0.21.5'
  - '@esbuild/win32-arm64@0.21.5'
  - '@esbuild/win32-ia32@0.21.5'
  - '@esbuild/win32-x64@0.21.5'
  - '@rollup/rollup-android-arm-eabi@4.46.3'
  - '@rollup/rollup-android-arm64@4.46.3'
  - '@rollup/rollup-darwin-x64@4.46.3'
  - '@rollup/rollup-freebsd-arm64@4.46.3'
  - '@rollup/rollup-freebsd-x64@4.46.3'
  - '@rollup/rollup-linux-arm-gnueabihf@4.46.3'
  - '@rollup/rollup-linux-arm-musleabihf@4.46.3'
  - '@rollup/rollup-linux-arm64-gnu@4.46.3'
  - '@rollup/rollup-linux-arm64-musl@4.46.3'
  - '@rollup/rollup-linux-loongarch64-gnu@4.46.3'
  - '@rollup/rollup-linux-ppc64-gnu@4.46.3'
  - '@rollup/rollup-linux-riscv64-gnu@4.46.3'
  - '@rollup/rollup-linux-riscv64-musl@4.46.3'
  - '@rollup/rollup-linux-s390x-gnu@4.46.3'
  - '@rollup/rollup-linux-x64-gnu@4.46.3'
  - '@rollup/rollup-linux-x64-musl@4.46.3'
  - '@rollup/rollup-win32-arm64-msvc@4.46.3'
  - '@rollup/rollup-win32-ia32-msvc@4.46.3'
  - '@rollup/rollup-win32-x64-msvc@4.46.3'
storeDir: /Users/<USER>/Library/pnpm/store/v10
virtualStoreDir: .pnpm
virtualStoreDirMaxLength: 120
