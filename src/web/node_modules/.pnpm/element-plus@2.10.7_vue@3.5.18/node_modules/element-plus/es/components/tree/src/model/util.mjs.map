{"version": 3, "file": "util.mjs", "sources": ["../../../../../../../packages/components/tree/src/model/util.ts"], "sourcesContent": ["import type { SetupContext } from 'vue'\nimport type Node from './node'\nimport type { RootTreeType, TreeKey, TreeNodeData } from '../tree.type'\n\nexport const NODE_KEY = '$treeNodeId'\n\nexport const markNodeData = function (\n  node: Node,\n  data: TreeNodeData | null\n): void {\n  if (!data || data[NODE_KEY]) return\n  Object.defineProperty(data, NODE_KEY, {\n    value: node.id,\n    enumerable: false,\n    configurable: false,\n    writable: false,\n  })\n}\n\nexport const getNodeKey = (key: TreeKey | undefined, data: TreeNodeData) =>\n  data?.[key || NODE_KEY]\n\nexport const handleCurrentChange = (\n  store: RootTreeType['store'],\n  emit: SetupContext['emit'],\n  setCurrent: () => void\n) => {\n  const preCurrentNode = store.value.currentNode\n  setCurrent()\n  const currentNode = store.value.currentNode\n  if (preCurrentNode === currentNode) return\n\n  emit('current-change', currentNode ? currentNode.data : null, currentNode)\n}\n"], "names": [], "mappings": "AAAY,MAAC,QAAQ,GAAG,cAAc;AAC1B,MAAC,YAAY,GAAG,SAAS,IAAI,EAAE,IAAI,EAAE;AACjD,EAAE,IAAI,CAAC,IAAI,IAAI,IAAI,CAAC,QAAQ,CAAC;AAC7B,IAAI,OAAO;AACX,EAAE,MAAM,CAAC,cAAc,CAAC,IAAI,EAAE,QAAQ,EAAE;AACxC,IAAI,KAAK,EAAE,IAAI,CAAC,EAAE;AAClB,IAAI,UAAU,EAAE,KAAK;AACrB,IAAI,YAAY,EAAE,KAAK;AACvB,IAAI,QAAQ,EAAE,KAAK;AACnB,GAAG,CAAC,CAAC;AACL,EAAE;AACU,MAAC,UAAU,GAAG,CAAC,GAAG,EAAE,IAAI,KAAK,IAAI,IAAI,IAAI,GAAG,KAAK,CAAC,GAAG,IAAI,CAAC,GAAG,IAAI,QAAQ,EAAE;AAC3E,MAAC,mBAAmB,GAAG,CAAC,KAAK,EAAE,IAAI,EAAE,UAAU,KAAK;AAChE,EAAE,MAAM,cAAc,GAAG,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC;AACjD,EAAE,UAAU,EAAE,CAAC;AACf,EAAE,MAAM,WAAW,GAAG,KAAK,CAAC,KAAK,CAAC,WAAW,CAAC;AAC9C,EAAE,IAAI,cAAc,KAAK,WAAW;AACpC,IAAI,OAAO;AACX,EAAE,IAAI,CAAC,gBAAgB,EAAE,WAAW,GAAG,WAAW,CAAC,IAAI,GAAG,IAAI,EAAE,WAAW,CAAC,CAAC;AAC7E;;;;"}