{"version": 3, "file": "transfer-panel2.mjs", "sources": ["../../../../../../packages/components/transfer/src/transfer-panel.vue"], "sourcesContent": ["<template>\n  <div :class=\"ns.b('panel')\">\n    <p :class=\"ns.be('panel', 'header')\">\n      <el-checkbox\n        v-model=\"allChecked\"\n        :indeterminate=\"isIndeterminate\"\n        :validate-event=\"false\"\n        @change=\"handleAllCheckedChange\"\n      >\n        {{ title }}\n        <span>{{ checkedSummary }}</span>\n      </el-checkbox>\n    </p>\n\n    <div :class=\"[ns.be('panel', 'body'), ns.is('with-footer', hasFooter)]\">\n      <el-input\n        v-if=\"filterable\"\n        v-model=\"query\"\n        :class=\"ns.be('panel', 'filter')\"\n        size=\"default\"\n        :placeholder=\"placeholder\"\n        :prefix-icon=\"Search\"\n        clearable\n        :validate-event=\"false\"\n      />\n      <el-checkbox-group\n        v-show=\"!hasNoMatch && !isEmpty(data)\"\n        v-model=\"checked\"\n        :validate-event=\"false\"\n        :class=\"[ns.is('filterable', filterable), ns.be('panel', 'list')]\"\n      >\n        <el-checkbox\n          v-for=\"item in filteredData\"\n          :key=\"item[propsAlias.key]\"\n          :class=\"ns.be('panel', 'item')\"\n          :value=\"item[propsAlias.key]\"\n          :disabled=\"item[propsAlias.disabled]\"\n          :validate-event=\"false\"\n        >\n          <option-content :option=\"optionRender?.(item)\" />\n        </el-checkbox>\n      </el-checkbox-group>\n      <div\n        v-show=\"hasNoMatch || isEmpty(data)\"\n        :class=\"ns.be('panel', 'empty')\"\n      >\n        <slot name=\"empty\">\n          {{ hasNoMatch ? t('el.transfer.noMatch') : t('el.transfer.noData') }}\n        </slot>\n      </div>\n    </div>\n    <p v-if=\"hasFooter\" :class=\"ns.be('panel', 'footer')\">\n      <slot />\n    </p>\n  </div>\n</template>\n\n<script lang=\"ts\" setup>\nimport { computed, reactive, toRefs, useSlots } from 'vue'\nimport { isEmpty } from '@element-plus/utils'\nimport { useLocale, useNamespace } from '@element-plus/hooks'\nimport { ElCheckbox, ElCheckboxGroup } from '@element-plus/components/checkbox'\nimport { ElInput } from '@element-plus/components/input'\nimport { Search } from '@element-plus/icons-vue'\nimport { transferPanelEmits, transferPanelProps } from './transfer-panel'\nimport { useCheck, usePropsAlias } from './composables'\n\nimport type { VNode } from 'vue'\nimport type { TransferPanelState } from './transfer-panel'\n\ndefineOptions({\n  name: 'ElTransferPanel',\n})\n\nconst props = defineProps(transferPanelProps)\nconst emit = defineEmits(transferPanelEmits)\nconst slots = useSlots()\n\nconst OptionContent = ({ option }: { option?: VNode | VNode[] }) => option\n\nconst { t } = useLocale()\nconst ns = useNamespace('transfer')\n\nconst panelState = reactive<TransferPanelState>({\n  checked: [],\n  allChecked: false,\n  query: '',\n  checkChangeByUser: true,\n})\n\nconst propsAlias = usePropsAlias(props)\n\nconst {\n  filteredData,\n  checkedSummary,\n  isIndeterminate,\n  handleAllCheckedChange,\n} = useCheck(props, panelState, emit)\n\nconst hasNoMatch = computed(\n  () => !isEmpty(panelState.query) && isEmpty(filteredData.value)\n)\n\nconst hasFooter = computed(() => !isEmpty(slots.default!()[0].children))\n\nconst { checked, allChecked, query } = toRefs(panelState)\n\ndefineExpose({\n  /** @description filter keyword */\n  query,\n})\n</script>\n"], "names": ["_openBlock", "_createElementBlock", "_normalizeClass", "_unref"], "mappings": ";;;;;;;;;;;;mCAsEc,CAAA;AAAA,EACZ,IAAM,EAAA,iBAAA;AACR,CAAA,CAAA,CAAA;;;;;;;AAIA,IAAA,MAAM,QAAQ,QAAS,EAAA,CAAA;AAEvB,IAAA,MAAM,aAAgB,GAAA,CAAC,EAAE,MAAA,EAA2C,KAAA,MAAA,CAAA;AAEpE,IAAM,MAAA,EAAE,CAAE,EAAA,GAAI,SAAU,EAAA,CAAA;AACxB,IAAM,MAAA,EAAA,GAAK,aAAa,UAAU,CAAA,CAAA;AAElC,IAAA,MAAM,aAAa,QAA6B,CAAA;AAAA,MAC9C,SAAS,EAAC;AAAA,MACV,UAAY,EAAA,KAAA;AAAA,MACZ,KAAO,EAAA,EAAA;AAAA,MACP,iBAAmB,EAAA,IAAA;AAAA,KACpB,CAAA,CAAA;AAED,IAAM,MAAA,UAAA,GAAa,cAAc,KAAK,CAAA,CAAA;AAEtC,IAAM,MAAA;AAAA,MACJ,YAAA;AAAA,MACA,cAAA;AAAA,MACA,eAAA;AAAA,MACA,sBAAA;AAAA,KACE,GAAA,QAAA,CAAS,KAAO,EAAA,UAAA,EAAY,IAAI,CAAA,CAAA;AAEpC,IAAA,MAAM,UAAa,GAAA,QAAA,CAAA,MAAA,CAAA,OAAA,CAAA,UAAA,CAAA,KAAA,CAAA,IAAA,OAAA,CAAA,YAAA,CAAA,KAAA,CAAA,CAAA,CAAA;AAAA,IACjB,MAAA,SAAe,GAAA,QAAA,CAAA,MAAgB,CAAK,OAAA,CAAA,iBAAqB,CAAK,CAAA,QAAA,CAAA,CAAA,CAAA;AAAA,IAChE,MAAA,EAAA,OAAA,EAAA,UAAA,EAAA,KAAA,EAAA,GAAA,MAAA,CAAA,UAAA,CAAA,CAAA;AAEA,IAAM,MAAA,CAAA;AAEN,MAAA;AAEA,KAAa,CAAA,CAAA;AAAA,IAAA,OAAA,CAAA,IAAA,EAAA,MAAA,KAAA;AAAA,MAEX,OAAAA,SAAA,EAAA,EAAAC,kBAAA,CAAA,KAAA,EAAA;AAAA,QACD,KAAA,EAAAC,cAAA,CAAAC,KAAA,CAAA,EAAA,CAAA,CAAA,CAAA,CAAA,OAAA,CAAA,CAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;"}