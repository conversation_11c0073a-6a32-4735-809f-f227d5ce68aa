{"version": 3, "file": "use-check.mjs", "sources": ["../../../../../../../packages/components/transfer/src/composables/use-check.ts"], "sourcesContent": ["import { computed, watch } from 'vue'\nimport { isFunction } from '@element-plus/utils'\nimport { CHECKED_CHANGE_EVENT } from '../transfer-panel'\nimport { usePropsAlias } from './use-props-alias'\n\nimport type { SetupContext } from 'vue'\nimport type { CheckboxValueType } from '@element-plus/components/checkbox'\nimport type { TransferKey } from '../transfer'\nimport type {\n  TransferPanelEmits,\n  TransferPanelProps,\n  TransferPanelState,\n} from '../transfer-panel'\n\nexport const useCheck = (\n  props: TransferPanelProps,\n  panelState: TransferPanelState,\n  emit: SetupContext<TransferPanelEmits>['emit']\n) => {\n  const propsAlias = usePropsAlias(props)\n\n  const filteredData = computed(() => {\n    return props.data.filter((item) => {\n      if (isFunction(props.filterMethod)) {\n        return props.filterMethod(panelState.query, item)\n      } else {\n        const label = String(\n          item[propsAlias.value.label] || item[propsAlias.value.key]\n        )\n        return label.toLowerCase().includes(panelState.query.toLowerCase())\n      }\n    })\n  })\n\n  const checkableData = computed(() =>\n    filteredData.value.filter((item) => !item[propsAlias.value.disabled])\n  )\n\n  const checkedSummary = computed(() => {\n    const checkedLength = panelState.checked.length\n    const dataLength = props.data.length\n    const { noChecked, hasChecked } = props.format\n\n    if (noChecked && hasChecked) {\n      return checkedLength > 0\n        ? hasChecked\n            .replace(/\\${checked}/g, checkedLength.toString())\n            .replace(/\\${total}/g, dataLength.toString())\n        : noChecked.replace(/\\${total}/g, dataLength.toString())\n    } else {\n      return `${checkedLength}/${dataLength}`\n    }\n  })\n\n  const isIndeterminate = computed(() => {\n    const checkedLength = panelState.checked.length\n    return checkedLength > 0 && checkedLength < checkableData.value.length\n  })\n\n  const updateAllChecked = () => {\n    const checkableDataKeys = checkableData.value.map(\n      (item) => item[propsAlias.value.key]\n    )\n    panelState.allChecked =\n      checkableDataKeys.length > 0 &&\n      checkableDataKeys.every((item) => panelState.checked.includes(item))\n  }\n\n  const handleAllCheckedChange = (value: CheckboxValueType) => {\n    panelState.checked = value\n      ? checkableData.value.map((item) => item[propsAlias.value.key])\n      : []\n  }\n\n  watch(\n    () => panelState.checked,\n    (val, oldVal) => {\n      updateAllChecked()\n\n      if (panelState.checkChangeByUser) {\n        const movedKeys = val\n          .concat(oldVal)\n          .filter((v) => !val.includes(v) || !oldVal.includes(v))\n        emit(CHECKED_CHANGE_EVENT, val, movedKeys)\n      } else {\n        emit(CHECKED_CHANGE_EVENT, val)\n        panelState.checkChangeByUser = true\n      }\n    }\n  )\n\n  watch(checkableData, () => {\n    updateAllChecked()\n  })\n\n  watch(\n    () => props.data,\n    () => {\n      const checked: TransferKey[] = []\n      const filteredDataKeys = filteredData.value.map(\n        (item) => item[propsAlias.value.key]\n      )\n      panelState.checked.forEach((item) => {\n        if (filteredDataKeys.includes(item)) {\n          checked.push(item)\n        }\n      })\n      panelState.checkChangeByUser = false\n      panelState.checked = checked\n    }\n  )\n\n  watch(\n    () => props.defaultChecked,\n    (val, oldVal) => {\n      if (\n        oldVal &&\n        val.length === oldVal.length &&\n        val.every((item) => oldVal.includes(item))\n      )\n        return\n\n      const checked: TransferKey[] = []\n      const checkableDataKeys = checkableData.value.map(\n        (item) => item[propsAlias.value.key]\n      )\n\n      val.forEach((item) => {\n        if (checkableDataKeys.includes(item)) {\n          checked.push(item)\n        }\n      })\n      panelState.checkChangeByUser = false\n      panelState.checked = checked\n    },\n    {\n      immediate: true,\n    }\n  )\n\n  return {\n    filteredData,\n    checkableData,\n    checkedSummary,\n    isIndeterminate,\n    updateAllChecked,\n    handleAllCheckedChange,\n  }\n}\n"], "names": [], "mappings": ";;;;;AAIY,MAAC,QAAQ,GAAG,CAAC,KAAK,EAAE,UAAU,EAAE,IAAI,KAAK;AACrD,EAAE,MAAM,UAAU,GAAG,aAAa,CAAC,KAAK,CAAC,CAAC;AAC1C,EAAE,MAAM,YAAY,GAAG,QAAQ,CAAC,MAAM;AACtC,IAAI,OAAO,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK;AACvC,MAAM,IAAI,UAAU,CAAC,KAAK,CAAC,YAAY,CAAC,EAAE;AAC1C,QAAQ,OAAO,KAAK,CAAC,YAAY,CAAC,UAAU,CAAC,KAAK,EAAE,IAAI,CAAC,CAAC;AAC1D,OAAO,MAAM;AACb,QAAQ,MAAM,KAAK,GAAG,MAAM,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,CAAC,IAAI,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;AACzF,QAAQ,OAAO,KAAK,CAAC,WAAW,EAAE,CAAC,QAAQ,CAAC,UAAU,CAAC,KAAK,CAAC,WAAW,EAAE,CAAC,CAAC;AAC5E,OAAO;AACP,KAAK,CAAC,CAAC;AACP,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,aAAa,GAAG,QAAQ,CAAC,MAAM,YAAY,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,IAAI,KAAK,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;AAC9G,EAAE,MAAM,cAAc,GAAG,QAAQ,CAAC,MAAM;AACxC,IAAI,MAAM,aAAa,GAAG,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC;AACpD,IAAI,MAAM,UAAU,GAAG,KAAK,CAAC,IAAI,CAAC,MAAM,CAAC;AACzC,IAAI,MAAM,EAAE,SAAS,EAAE,UAAU,EAAE,GAAG,KAAK,CAAC,MAAM,CAAC;AACnD,IAAI,IAAI,SAAS,IAAI,UAAU,EAAE;AACjC,MAAM,OAAO,aAAa,GAAG,CAAC,GAAG,UAAU,CAAC,OAAO,CAAC,cAAc,EAAE,aAAa,CAAC,QAAQ,EAAE,CAAC,CAAC,OAAO,CAAC,YAAY,EAAE,UAAU,CAAC,QAAQ,EAAE,CAAC,GAAG,SAAS,CAAC,OAAO,CAAC,YAAY,EAAE,UAAU,CAAC,QAAQ,EAAE,CAAC,CAAC;AACpM,KAAK,MAAM;AACX,MAAM,OAAO,CAAC,EAAE,aAAa,CAAC,CAAC,EAAE,UAAU,CAAC,CAAC,CAAC;AAC9C,KAAK;AACL,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,eAAe,GAAG,QAAQ,CAAC,MAAM;AACzC,IAAI,MAAM,aAAa,GAAG,UAAU,CAAC,OAAO,CAAC,MAAM,CAAC;AACpD,IAAI,OAAO,aAAa,GAAG,CAAC,IAAI,aAAa,GAAG,aAAa,CAAC,KAAK,CAAC,MAAM,CAAC;AAC3E,GAAG,CAAC,CAAC;AACL,EAAE,MAAM,gBAAgB,GAAG,MAAM;AACjC,IAAI,MAAM,iBAAiB,GAAG,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;AAC5F,IAAI,UAAU,CAAC,UAAU,GAAG,iBAAiB,CAAC,MAAM,GAAG,CAAC,IAAI,iBAAiB,CAAC,KAAK,CAAC,CAAC,IAAI,KAAK,UAAU,CAAC,OAAO,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC,CAAC;AACjI,GAAG,CAAC;AACJ,EAAE,MAAM,sBAAsB,GAAG,CAAC,KAAK,KAAK;AAC5C,IAAI,UAAU,CAAC,OAAO,GAAG,KAAK,GAAG,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;AACpG,GAAG,CAAC;AACJ,EAAE,KAAK,CAAC,MAAM,UAAU,CAAC,OAAO,EAAE,CAAC,GAAG,EAAE,MAAM,KAAK;AACnD,IAAI,gBAAgB,EAAE,CAAC;AACvB,IAAI,IAAI,UAAU,CAAC,iBAAiB,EAAE;AACtC,MAAM,MAAM,SAAS,GAAG,GAAG,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,KAAK,CAAC,GAAG,CAAC,QAAQ,CAAC,CAAC,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC,CAAC;AAClG,MAAM,IAAI,CAAC,oBAAoB,EAAE,GAAG,EAAE,SAAS,CAAC,CAAC;AACjD,KAAK,MAAM;AACX,MAAM,IAAI,CAAC,oBAAoB,EAAE,GAAG,CAAC,CAAC;AACtC,MAAM,UAAU,CAAC,iBAAiB,GAAG,IAAI,CAAC;AAC1C,KAAK;AACL,GAAG,CAAC,CAAC;AACL,EAAE,KAAK,CAAC,aAAa,EAAE,MAAM;AAC7B,IAAI,gBAAgB,EAAE,CAAC;AACvB,GAAG,CAAC,CAAC;AACL,EAAE,KAAK,CAAC,MAAM,KAAK,CAAC,IAAI,EAAE,MAAM;AAChC,IAAI,MAAM,OAAO,GAAG,EAAE,CAAC;AACvB,IAAI,MAAM,gBAAgB,GAAG,YAAY,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;AAC1F,IAAI,UAAU,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK;AACzC,MAAM,IAAI,gBAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;AAC3C,QAAQ,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC3B,OAAO;AACP,KAAK,CAAC,CAAC;AACP,IAAI,UAAU,CAAC,iBAAiB,GAAG,KAAK,CAAC;AACzC,IAAI,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;AACjC,GAAG,CAAC,CAAC;AACL,EAAE,KAAK,CAAC,MAAM,KAAK,CAAC,cAAc,EAAE,CAAC,GAAG,EAAE,MAAM,KAAK;AACrD,IAAI,IAAI,MAAM,IAAI,GAAG,CAAC,MAAM,KAAK,MAAM,CAAC,MAAM,IAAI,GAAG,CAAC,KAAK,CAAC,CAAC,IAAI,KAAK,MAAM,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;AAC5F,MAAM,OAAO;AACb,IAAI,MAAM,OAAO,GAAG,EAAE,CAAC;AACvB,IAAI,MAAM,iBAAiB,GAAG,aAAa,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,IAAI,KAAK,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;AAC5F,IAAI,GAAG,CAAC,OAAO,CAAC,CAAC,IAAI,KAAK;AAC1B,MAAM,IAAI,iBAAiB,CAAC,QAAQ,CAAC,IAAI,CAAC,EAAE;AAC5C,QAAQ,OAAO,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;AAC3B,OAAO;AACP,KAAK,CAAC,CAAC;AACP,IAAI,UAAU,CAAC,iBAAiB,GAAG,KAAK,CAAC;AACzC,IAAI,UAAU,CAAC,OAAO,GAAG,OAAO,CAAC;AACjC,GAAG,EAAE;AACL,IAAI,SAAS,EAAE,IAAI;AACnB,GAAG,CAAC,CAAC;AACL,EAAE,OAAO;AACT,IAAI,YAAY;AAChB,IAAI,aAAa;AACjB,IAAI,cAAc;AAClB,IAAI,eAAe;AACnB,IAAI,gBAAgB;AACpB,IAAI,sBAAsB;AAC1B,GAAG,CAAC;AACJ;;;;"}