{"version": 3, "file": "index.mjs", "sources": ["../../../../../packages/components/alert/index.ts"], "sourcesContent": ["import { withInstall } from '@element-plus/utils'\nimport Alert from './src/alert.vue'\n\nimport type { SFCWithInstall } from '@element-plus/utils'\n\nexport const ElAlert: SFCWithInstall<typeof Alert> = withInstall(Alert)\nexport default ElAlert\n\nexport * from './src/alert'\nexport type { AlertInstance } from './src/instance'\n"], "names": [], "mappings": ";;;;AAEY,MAAC,OAAO,GAAG,WAAW,CAAC,KAAK;;;;"}