import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'
import { resolve } from 'path'

export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': resolve(__dirname, 'src')
    }
  },
  server: {
    port: 3000,
    strictPort: false, // 允许端口轮换
    proxy: {
      '/api': {
        target: 'http://localhost:2026',
        changeOrigin: true
      }
    }
  }
})
