"""
LLM配置系统 - 智能重试架构

设计原则：
1. 模式分类：json_mode 和 text_mode
2. 数字化tier：数字越小优先级越高
3. 智能错误处理：某些错误立即切换模型
4. 同级多模型：支持同一级别配置多个模型
5. 用户可配置：提供清晰的配置接口
"""

import logging
import random
from dataclasses import dataclass, field
from enum import Enum
from typing import Dict, List, Optional, Union

logger = logging.getLogger(__name__)


# 为了向后兼容，保留ModelTier枚举，但映射到数字
class ModelTier(Enum):
    """模型等级枚举 - 向后兼容"""

    LOW = 1
    MEDIUM = 2
    HIGH = 3


class RequestMode(Enum):
    """请求模式枚举"""

    JSON_MODE = "json_mode"  # 结构化输出请求
    TEXT_MODE = "text_mode"  # 文本输出请求


class ModelTier(Enum):
    """模型等级枚举"""

    LOW = "low"  # 低级模型：快速、便宜
    MEDIUM = "medium"  # 中级模型：平衡性能和成本
    HIGH = "high"  # 高级模型：最高质量


@dataclass
class ModelConfig:
    """单个模型配置"""

    name: str
    weight: float = 1.0  # 同级模型的权重，用于负载均衡
    tier: int = 1  # tier等级，数字越小优先级越高

    def __post_init__(self):
        if self.weight <= 0:
            raise ValueError(f"Model weight must be positive, got {self.weight}")
        if self.tier < 1:
            raise ValueError(f"Model tier must be >= 1, got {self.tier}")


@dataclass
class TierConfig:
    """等级配置，包含该等级的所有模型"""

    models: List[ModelConfig] = field(default_factory=list)

    def add_model(self, name: str, weight: float = 1.0, tier: int = 1) -> None:
        """添加模型到该等级"""
        self.models.append(ModelConfig(name=name, weight=weight, tier=tier))

    def get_random_model(self) -> Optional[str]:
        """根据权重随机选择一个模型"""
        if not self.models:
            return None

        if len(self.models) == 1:
            return self.models[0].name

        # 权重随机选择
        weights = [model.weight for model in self.models]
        selected = random.choices(self.models, weights=weights, k=1)[0]
        return selected.name

    def get_all_models(self) -> List[str]:
        """获取该等级的所有模型名称"""
        return [model.name for model in self.models]


@dataclass
class ModeConfig:
    """模式配置，包含该模式下的所有tier"""

    tiers: Dict[int, TierConfig] = field(default_factory=dict)

    def add_model(self, name: str, weight: float = 1.0, tier: int = 1) -> None:
        """添加模型到指定tier"""
        if tier not in self.tiers:
            self.tiers[tier] = TierConfig()
        self.tiers[tier].add_model(name, weight, tier)

    def get_model_for_tier(self, tier: Union[ModelTier, int]) -> Optional[str]:
        """获取指定tier的模型"""
        tier_num = tier.value if isinstance(tier, ModelTier) else tier
        if tier_num not in self.tiers:
            return None
        return self.tiers[tier_num].get_random_model()

    def get_retry_sequence(self) -> List[int]:
        """获取重试序列：按tier数字从小到大排序"""
        return sorted(self.tiers.keys())

    # 向后兼容的方法
    def get_tier_config(self, tier: ModelTier) -> TierConfig:
        """获取指定等级的配置 - 向后兼容"""
        tier_num = tier.value
        if tier_num not in self.tiers:
            self.tiers[tier_num] = TierConfig()
        return self.tiers[tier_num]


class LLMConfigManager:
    """LLM配置管理器"""

    def __init__(self):
        self.configs: Dict[RequestMode, ModeConfig] = {
            RequestMode.JSON_MODE: ModeConfig(),
            RequestMode.TEXT_MODE: ModeConfig(),
        }
        # 每个tier内的连续尝试次数，之后才升级到下一tier
        self.retries_per_tier: int = 1  # 减少无效重试
        self._setup_default_config()

    def _setup_default_config(self):
        """从JSON配置文件加载默认配置"""
        try:
            # 导入配置
            from src.langgraph.common.models.llm_models_config import LLM_CONFIG, validate_config

            # 验证配置
            errors = validate_config()
            if errors:
                logger.error(f"LLM配置验证失败: {errors}")
                raise ValueError(f"LLM配置验证失败: {errors}")

            # 加载JSON模式配置
            json_config = self.configs[RequestMode.JSON_MODE]
            json_config.tiers.clear()  # 清空现有配置
            for model_info in LLM_CONFIG["json_mode"]:
                json_config.add_model(model_info["name"], model_info["weight"], model_info["tier"])

            # 加载TEXT模式配置
            text_config = self.configs[RequestMode.TEXT_MODE]
            text_config.tiers.clear()  # 清空现有配置
            for model_info in LLM_CONFIG["text_mode"]:
                text_config.add_model(model_info["name"], model_info["weight"], model_info["tier"])

            logger.info("LLM配置加载成功")

        except Exception as e:
            logger.error(f"加载LLM配置失败: {e}")
            raise e

    def get_model_for_request(
        self, mode: RequestMode, tier: Optional[Union[ModelTier, int]] = None, retry_index: int = 0
    ) -> Optional[str]:
        """
        获取请求使用的模型

        Args:
            mode: 请求模式
            tier: 指定的tier（ModelTier枚举或数字，可选）
            retry_index: 重试索引，用于自动升级tier

        Returns:
            模型名称，如果没有可用模型则返回None
        """
        mode_config = self.configs[mode]
        retry_sequence = mode_config.get_retry_sequence()

        if not retry_sequence:
            logger.warning(f"No models configured for mode: {mode.value}")
            return None

        # 确定起始tier
        if tier is None:
            start_tier_num = retry_sequence[0] if retry_sequence else 1
        elif isinstance(tier, ModelTier):
            start_tier_num = tier.value
        else:
            start_tier_num = tier

        # 从起始tier开始的索引
        try:
            start_index = retry_sequence.index(start_tier_num)
        except ValueError:
            # 如果指定的tier不存在，从第一个tier开始
            logger.warning(
                f"Requested tier {start_tier_num} not available in {mode.value} config; using tier {retry_sequence[0]}"
            )
            start_index = 0

        # 计算需要升级的等级步数：每个等级内部尝试retries_per_tier次
        tier_offset = retry_index // max(1, self.retries_per_tier)
        actual_index = start_index + tier_offset

        if actual_index >= len(retry_sequence):
            logger.warning(
                f"No more models available after retry #{retry_index} (retries_per_tier={self.retries_per_tier}) in {mode.value} mode"
            )
            return None

        actual_tier_num = retry_sequence[actual_index]
        model = mode_config.get_model_for_tier(actual_tier_num)
        if model:
            if retry_index > 0:
                logger.info(
                    f"Retry #{retry_index}: Using tier {actual_tier_num} model (retries_per_tier={self.retries_per_tier}): {model}"
                )
            return model

        logger.warning(f"No model found for tier {actual_tier_num} on retry #{retry_index} in {mode.value} mode")
        return None

    def get_max_retries(self, mode: RequestMode) -> int:
        """获取指定模式的最大重试次数（不含第一次尝试）

        计算方式：可用等级数量 × 每等级尝试次数 - 1
        举例：3个等级、每等级3次 => 3×3-1=8（循环会从0到8，共9次尝试）
        """
        mode_config = self.configs[mode]
        tiers_count = len(mode_config.get_retry_sequence())
        if tiers_count == 0:
            return 0
        return tiers_count * max(1, self.retries_per_tier) - 1

    def set_retries_per_tier(self, retries: int) -> None:
        """设置每个等级内的连续尝试次数"""
        if retries < 1:
            raise ValueError(f"retries_per_tier must be >= 1, got {retries}")
        self.retries_per_tier = retries

    def configure_mode(self, mode: RequestMode, tier: ModelTier, models: List[Union[str, tuple]]) -> None:
        """
        配置指定模式和等级的模型

        Args:
            mode: 请求模式
            tier: 模型等级
            models: 模型列表，可以是字符串或(name, weight)元组
        """
        mode_config = self.configs[mode]
        tier_config = mode_config.get_tier_config(tier)

        # 清空现有配置
        tier_config.models.clear()

        # 添加新配置
        for model in models:
            if isinstance(model, str):
                tier_config.add_model(model)
            elif isinstance(model, tuple) and len(model) == 2:
                name, weight = model
                tier_config.add_model(name, weight)
            else:
                raise ValueError(f"Invalid model format: {model}")

        logger.info(f"Configured {mode.value} {tier.value} tier with {len(models)} models")


# 全局配置管理器实例
llm_config_manager = LLMConfigManager()


def get_model_for_request(mode: RequestMode, tier: ModelTier = ModelTier.LOW, retry_index: int = 0) -> Optional[str]:
    """便捷函数：获取请求使用的模型"""
    return llm_config_manager.get_model_for_request(mode, tier, retry_index)


def get_max_retries(mode: RequestMode) -> int:
    """便捷函数：获取最大重试次数"""
    return llm_config_manager.get_max_retries(mode)


def should_immediately_switch_model(error: Exception) -> bool:
    """
    判断是否应该立即切换模型而不是重试

    Args:
        error: 异常对象

    Returns:
        True表示应该立即切换模型，False表示可以重试
    """
    error_str = str(error).lower()

    # Token限制相关错误
    token_limit_indicators = [
        "range of input length should be",
        "input length",
        "token limit",
        "context length",
        "maximum context",
        "too many tokens",
        "input too long",
    ]

    # 模型不支持的功能
    unsupported_indicators = ["not supported", "invalid parameter", "unsupported", "not available"]

    # 检查是否是应该立即切换的错误
    for indicator in token_limit_indicators + unsupported_indicators:
        if indicator in error_str:
            return True

    return False
