import logging
import typing
from collections.abc import AsyncIterator
from contextlib import asynccontextmanager
from functools import wraps
from typing import Any, List, Optional, Union

from langchain_core.messages import AIMessage, BaseMessage, HumanMessage, SystemMessage

from langchain_core.runnables.utils import Output

from src.langgraph.common.models.llm_config import (
    get_max_retries,
    get_model_for_request,
    llm_config_manager,
    ModelTier,
    RequestMode,
    should_immediately_switch_model,
)
from src.langgraph.common.models.llms import get_llm_by_model_name
from src.langgraph.common.utils.json_utils import _looks_like_schema_dict, _looks_like_schema_text, repair_json_output
from src.langgraph.common.utils.message_utils import MessageUtils
from src.langgraph.common.utils.token_utils import TokenTools

logger = logging.getLogger(__name__)

# 直接使用统一的模型管理系统（已集成智能重试功能）


def _anti_schema_messages() -> list[BaseMessage]:
    """当模型出现 schema 回显倾向时，建议追加到提示里的系统/人类约束消息。"""
    return [
        SystemMessage(
            content=(
                "严格只输出一个JSON数据对象；不要返回或复制schema文本；"
                "不要使用任何Markdown代码块（如```或```json）；不要输出解释性文字。"
            )
        ),
        HumanMessage(content="请给我内容，而不是schema"),
    ]


from src.langgraph.common.utils.socket_router import ensure_socket_patched, plain_socket_context


# Ensure socket is patched once so per-context routing is active
ensure_socket_patched()


@asynccontextmanager
async def llm_request_context():
    """LLM 请求上下文：强制使用直连 socket，杜绝 SOCKS 影响。"""
    async with plain_socket_context():
        yield


def llm_safe_request(func):
    """
    装饰器：确保LLM请求在安全的网络环境中执行，不受SOCKS代理影响
    """

    @wraps(func)
    async def wrapper(*args, **kwargs):
        async with llm_request_context():
            return await func(*args, **kwargs)

    return wrapper


def create_token_update(input_tokens: int, output_tokens: int) -> dict:
    """
    创建token更新字典，用于更新状态中的token统计

    Args:
        input_tokens: 输入token数量
        output_tokens: 输出token数量

    Returns:
        包含token统计的字典
    """
    return {
        "total_input_tokens": input_tokens,
        "total_output_tokens": output_tokens,
    }


async def get_response_from_astream(chunks: AsyncIterator[Output]) -> Output:
    first = True
    async for chunk in chunks:
        if first:
            response = chunk
            first = False
        else:
            response = response + chunk

    if isinstance(response, dict):
        return response

    # 给glm4.5适配
    ori_content = response.content
    if "</think>" in ori_content:
        # 找到最后一个</think>，前后分为 part1 和 part2
        last_think_index = ori_content.rfind("</think>")
        part1 = ori_content[:last_think_index]
        part2 = ori_content[last_think_index + len("</think>") :]

        response.content = part2
        response.additional_kwargs["reasoning_content"] = part1.replace("<think>", "").replace("</think>", "")

    return response


@llm_safe_request
async def llm_structured_request(
    node_name: str,
    input_messages: List[BaseMessage],
    schema_type: Union[typing.Dict, type],
    llm_params: dict[str, Any] = {},
    retry_index=0,
    max_retries: Optional[int] = None,
    tier: Optional[ModelTier] = ModelTier.LOW,
) -> tuple[
    AIMessage,
    Optional[Union[typing.Dict, type]],
    Optional[BaseException],
]:
    # 提前定义，避免在异常早于赋值时引用未绑定变量
    node_name = node_name

    # 确定最大重试次数
    if max_retries is None:
        max_retries = get_max_retries(RequestMode.JSON_MODE)

    last_error = None
    # 粘性模型：同一等级内重复使用同一模型，直到达到升级阈值
    sticky_model_name: Optional[str] = None
    retries_per_tier = getattr(llm_config_manager, "retries_per_tier", 1)

    # 当检测到模型回显schema时，动态追加的系统约束消息
    extra_system_messages: list[BaseMessage] = []
    anti_schema_hint_added = False

    for current_retry in range(max_retries + 1):  # +1 因为第一次不算重试
        try:
            # 智能重试逻辑：检查是否应该立即切换模型
            should_switch_immediately = False
            if current_retry > 0 and last_error:
                should_switch_immediately = should_immediately_switch_model(last_error)

            # 计算是否进入新的等级
            if (
                current_retry == 0
                or should_switch_immediately
                or (retries_per_tier > 0 and current_retry % retries_per_tier == 0)
            ):
                # 新等级、首次尝试或需要立即切换：重新选择模型
                model_name = (
                    get_model_for_request(RequestMode.JSON_MODE, tier, 0)
                    if current_retry == 0
                    else get_model_for_request(RequestMode.JSON_MODE, tier, current_retry)
                )
                sticky_model_name = model_name

                if should_switch_immediately:
                    logger.info(f"[{node_name}] Immediately switching model due to error: {type(last_error).__name__}")
            else:
                # 同一等级内复用同一模型
                model_name = sticky_model_name

            if model_name is None:
                logger.error(f"[{node_name}] No model available for retry #{current_retry}")
                break

            if current_retry > 0:
                logger.info(f"[{node_name}] Retry #{current_retry} using model: {model_name}")

            # 获取LLM实例
            messages_to_use: List[BaseMessage] = list(input_messages) + list(extra_system_messages)
            approximately_input_tokens = TokenTools.count_messages_approximately_tokens(messages_to_use)
            logger.info(f"[{node_name}] {llm_params=}, {approximately_input_tokens=} - {model_name}")
            llm = get_llm_by_model_name(model_name, llm_params)

            # 执行请求
            try:
                response = await llm.with_structured_output(schema_type, method="json_mode", include_raw=True).ainvoke(
                    messages_to_use
                )
            except Exception as e:
                logger.error(f"[{node_name}] ❌ 请求失败ainvoke,{e=}")
                try:
                    response = await get_response_from_astream(
                        llm.with_structured_output(schema_type, method="json_mode", include_raw=True).astream(
                            messages_to_use
                        )
                    )
                except Exception as e:
                    logger.error(f"[{node_name}] ❌ 请求失败astream,{e=}")
                    last_error = e
                    continue

            logger.info(f"[{node_name}] ✅ 请求成功，type(response)={type(response)}")

            # 解析输出
            response_raw, response_parsed, response_error = MessageUtils.parse_include_raw_response(response)

            # Token统计 - 初始化默认值以避免未定义变量错误
            input_tokens = 0
            output_tokens = 0
            if response_raw.usage_metadata is not None:
                input_tokens = response_raw.usage_metadata.get("input_tokens", 0)
                output_tokens = response_raw.usage_metadata.get("output_tokens", 0)
                _ = response_raw.usage_metadata.get("output_tokens", 0)
                TokenTools.fit_to_coefficient(messages_to_use, input_tokens)
                logger.info(f"[{node_name}] ✅ usage_metadata={response_raw.usage_metadata}")

            # 检查解析失败并尝试从原始文本修复/识别schema回显
            if response_parsed is None:
                raw_text = getattr(response_raw, "content", "")
                # 1) 优先尝试修复并解析为JSON，然后用目标schema强校验
                try:
                    import json

                    repaired = repair_json_output(raw_text)
                    parsed_json = None
                    try:
                        parsed_json = json.loads(repaired)
                    except Exception:
                        parsed_json = None
                    schema_cls = schema_type if isinstance(schema_type, type) else None
                    if parsed_json is not None and schema_cls is not None and hasattr(schema_cls, "model_validate"):
                        try:
                            parsed_obj = schema_cls.model_validate(parsed_json)
                            response_parsed = parsed_obj
                            response_error = None
                        except Exception as inner_validate_error:
                            # 2) 若能解析为JSON但不符合目标schema，判断是否为“schema回显”
                            if _looks_like_schema_dict(parsed_json):
                                logger.warning(
                                    f"[{node_name}] ❌检测到模型回显schema（解析为JSON但不匹配目标数据schema），追加系统约束并重试"
                                )
                                if not anti_schema_hint_added:
                                    extra_system_messages.extend(_anti_schema_messages())
                                    anti_schema_hint_added = True
                                last_error = inner_validate_error
                                continue
                            else:
                                # 非schema回显，保留校验错误继续走重试/升级
                                response_error = inner_validate_error
                    elif parsed_json is None:
                        # 3) JSON解析失败：在文本层面粗略识别schema回显（避免把正确数据错杀）
                        if _looks_like_schema_text(raw_text or ""):
                            if not anti_schema_hint_added:
                                logger.warning(f"[{node_name}] 文本疑似schema回显，追加系统约束并重试")
                                extra_system_messages.extend(_anti_schema_messages())
                                anti_schema_hint_added = True
                            last_error = ValueError("模型回显了schema（文本层面）")
                            continue
                except Exception as e:
                    response_error = response_error or e

            if response_parsed is None:
                error_msg = f"响应解析失败: {response_error}"
                logger.error(f"[{node_name}] ❌ {error_msg}")
                last_error = response_error or ValueError(error_msg)
                continue

            return response_raw, response_parsed, response_error, input_tokens, output_tokens

        except Exception as e:
            logger.error(f"[{node_name}] ❌请求异常: {e}")
            last_error = e
            continue

    # 所有重试都失败了
    logger.error(f"[{node_name}] ❌所有重试都失败，最后错误: {last_error}")
    raise last_error or RuntimeError("All retries failed")


@llm_safe_request
async def llm_str_request(
    node_name: str,
    input_messages: List[BaseMessage],
    llm_params: dict[str, Any] = {},
    stage_name: Optional[str] = None,
    tier: Optional[ModelTier] = ModelTier.LOW,
    max_retries: Optional[int] = None,
) -> tuple[str, str]:
    # 提前定义，避免在异常早于赋值时引用未绑定变量
    node_name = f"{node_name}:{stage_name}" if stage_name else node_name

    # 确定最大重试次数
    if max_retries is None:
        max_retries = get_max_retries(RequestMode.TEXT_MODE)

    last_error = None
    # 粘性模型：同一等级内重复使用同一模型，直到达到升级阈值
    sticky_model_name: Optional[str] = None
    retries_per_tier = getattr(llm_config_manager, "retries_per_tier", 1)

    for current_retry in range(max_retries + 1):  # +1 因为第一次不算重试
        try:
            # 智能重试逻辑：检查是否应该立即切换模型
            should_switch_immediately = False
            if current_retry > 0 and last_error:
                should_switch_immediately = should_immediately_switch_model(last_error)

            # 计算是否进入新的等级
            if (
                current_retry == 0
                or should_switch_immediately
                or (retries_per_tier > 0 and current_retry % retries_per_tier == 0)
            ):
                # 新等级、首次尝试或需要立即切换：重新选择模型
                model_name = (
                    get_model_for_request(RequestMode.TEXT_MODE, tier, 0)
                    if current_retry == 0
                    else get_model_for_request(RequestMode.TEXT_MODE, tier, current_retry)
                )
                sticky_model_name = model_name

                if should_switch_immediately:
                    logger.info(f"[{node_name}] Immediately switching model due to error: {type(last_error).__name__}")
            else:
                # 同一等级内复用同一模型
                model_name = sticky_model_name

            if model_name is None:
                logger.error(f"[{node_name}] No model available for retry #{current_retry}")
                break

            if current_retry > 0:
                logger.info(f"[{node_name}] Retry #{current_retry} using model: {model_name}")

            # 获取LLM实例
            approximately_input_tokens = TokenTools.count_messages_approximately_tokens(input_messages)
            logger.info(f"[{node_name}] {llm_params=}, {approximately_input_tokens=} - {model_name}")
            llm = get_llm_by_model_name(model_name, llm_params)

            # 执行请求
            response = await get_response_from_astream(llm.astream(input_messages))

            response_content = response.content
            response_cot = response.additional_kwargs.get("reasoning_content", "")

            logger.info(
                f"[{node_name}] response.content.len={len(response_content)}, cot.len={len(response_cot)}, token:{response.usage_metadata}"
            )
            if response.usage_metadata is not None:
                input_tokens = (
                    response.usage_metadata.get("input_tokens", -1) if hasattr(response.usage_metadata, "get") else None
                )
                TokenTools.fit_to_coefficient(input_messages, input_tokens)

            return response_content, response_cot

        except Exception as e:
            logger.error(f"[{node_name}] ❌请求异常: {e}")
            last_error = e
            continue

    # 所有重试都失败了
    logger.error(f"[{node_name}] ❌所有重试都失败，最后错误: {last_error}")
    return "", ""  # 文本模式返回空字符串而不是抛异常
