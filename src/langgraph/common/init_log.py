import logging
import logging.handlers
import os
from datetime import datetime

from src.langgraph.common.utils.path_utils import get_log_root


def init_log():
    """
    初始化日志系统

    优化特性：
    1. 使用RotatingFileHandler避免日志文件过大
    2. 按日期分割日志文件
    3. 自动清理旧日志文件
    4. 优化日志格式
    """
    LOG_DIR = get_log_root()
    os.makedirs(LOG_DIR, exist_ok=True)

    # 使用日期作为日志文件名，避免每次启动都创建新文件
    LOG_FILE_NAME = f"{LOG_DIR}/{datetime.now().strftime('%Y-%m-%d')}.log"

    # 创建格式化器
    formatter = logging.Formatter("%(asctime)s [%(name)s] %(levelname)s: %(message)s", datefmt="%Y-%m-%d %H:%M:%S")

    # 创建处理器
    handlers = []

    # 文件处理器 - 使用RotatingFileHandler
    file_handler = logging.handlers.RotatingFileHandler(
        LOG_FILE_NAME, maxBytes=50 * 1024 * 1024, backupCount=7, encoding="utf-8"  # 50MB  # 保留7个备份文件
    )
    file_handler.setFormatter(formatter)
    handlers.append(file_handler)

    # 控制台处理器
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(formatter)
    handlers.append(console_handler)

    # 配置根日志器
    logging.basicConfig(level=logging.INFO, handlers=handlers, force=True)  # 强制重新配置，避免重复初始化问题

    # 设置第三方库的日志级别，减少噪音
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("httpcore").setLevel(logging.WARNING)
    logging.getLogger("aiomysql").setLevel(logging.WARNING)
