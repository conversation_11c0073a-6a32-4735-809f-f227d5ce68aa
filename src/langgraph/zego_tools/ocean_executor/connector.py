"""
异步数据库连接器模块

要点：
- 使用 aiomysql 连接池（min/max 可配），支持连接复用与并发查询
- 通过 contextvars 实现按协程路由的 SOCKS 使用（见 socket_router）
- 去除进程级别的全局 socket 切换，避免并发竞态
"""

import asyncio
import contextlib
import logging
import os
from dataclasses import dataclass
from datetime import datetime
from typing import Any, Dict, Optional, Set

try:
    import aiomysql  # type: ignore
except Exception:  # pragma: no cover - allow environments without aiomysql
    aiomysql = None  # type: ignore
import pandas as pd
from dotenv import load_dotenv

from src.langgraph.common.utils.socket_router import db_socks_context, ensure_socket_patched

from .connection_monitor import connection_monitor, ConnectionState
from .types import SqlExecutorResult

load_dotenv(os.path.join(os.path.dirname(__file__), ".env"), override=True)

logger = logging.getLogger(__name__)


# 启用按协程路由的 socket 构造器（幂等）
ensure_socket_patched()


@dataclass
class ConnectorParams:
    """数据库连接配置参数（最小合并版本）

    通过环境变量名进行配置，避免在类中硬编码 ZEGO/THEMIS 差异。
    """

    # 基本连接相关环境变量名
    host_key: str
    port_key: str
    user_key: str
    password_key: str
    db_key: Optional[str]  # THEMIS 无固定 DB

    # 连接池配置环境变量名
    pool_min_key: str
    pool_max_key: str
    pool_recycle_key: str

    # 连接超时相关环境变量名（按优先级尝试）
    connect_timeout_keys: tuple[str, ...]

    # 其他
    default_port: int = 9030
    selects_db_per_query: bool = False  # THEMIS: True, OCEAN: False
    allowed_dbs: Optional[Set[str]] = None  # THEMIS: {sdk,access,opt,rtc,signal}
    dotenv_path: Optional[str] = None  # 指定需要加载的 .env 路径
    name: str = "db"  # 用于日志前缀


def _build_default_ocean_params() -> ConnectorParams:
    return ConnectorParams(
        host_key="ZEGO_MYSQL_URL",
        port_key="ZEGO_MYSQL_PORT",
        user_key="ZEGO_MYSQL_USER",
        password_key="ZEGO_MYSQL_PASSWORD",
        db_key="ZEGO_MYSQL_DB",
        pool_min_key="ZEGO_MYSQL_POOL_MIN",
        pool_max_key="ZEGO_MYSQL_POOL_MAX",
        pool_recycle_key="ZEGO_MYSQL_POOL_RECYCLE",
        connect_timeout_keys=("ZEGO_CONNECT_TIMEOUT", "DB_CONNECT_TIMEOUT"),
        default_port=9030,
        selects_db_per_query=False,
        allowed_dbs=None,
        dotenv_path=os.path.join(os.path.dirname(__file__), ".env"),
        name="ocean",
    )


def _build_default_themis_params() -> ConnectorParams:
    return ConnectorParams(
        host_key="THEMIS_URL",
        port_key="THEMIS_PORT",
        user_key="THEMIS_USER",
        password_key="THEMIS_PASSWORD",
        db_key=None,
        pool_min_key="THEMIS_POOL_MIN",
        pool_max_key="THEMIS_POOL_MAX",
        pool_recycle_key="THEMIS_POOL_RECYCLE",
        connect_timeout_keys=("THEMIS_CONNECT_TIMEOUT",),
        default_port=9030,
        selects_db_per_query=True,
        allowed_dbs={"sdk", "access", "opt", "rtc", "signal"},
        dotenv_path=os.path.join(os.path.dirname(os.path.dirname(__file__)), ".env"),
        name="themis",
    )


class AsyncDatabaseConnection:
    """异步数据库连接管理类（可配置版本，支持 ocean/themis 两套参数）"""

    def __init__(self, params: Optional[ConnectorParams] = None):
        """初始化连接管理器

        若未传入 params，默认按 ocean（ZEGO_MYSQL_*) 行为保持兼容。
        """
        self.params: ConnectorParams = params or _build_default_ocean_params()

        # 惰性加载 dotenv（幂等，允许重复调用）
        if self.params.dotenv_path:
            with contextlib.suppress(Exception):
                load_dotenv(self.params.dotenv_path, override=True)

        self.connection_pool: Any = None
        self._pool_lock: asyncio.Lock = asyncio.Lock()
        self.connection_status = {
            "is_connected": False,
            "last_connected": None,
            "host": os.getenv(self.params.host_key),
            "port": os.getenv(self.params.port_key, str(self.params.default_port)),
            "database": os.getenv(self.params.db_key) if self.params.db_key else None,
            "user": os.getenv(self.params.user_key),
            "error_msg": None,
        }

        logger.info(f"[{self.params.name}] {self.connection_status}")

    def get_status(self) -> Dict[str, Any]:
        """获取当前连接状态"""
        return self.connection_status.copy()

    def _update_status(self, is_connected: bool, error_msg: Optional[str] = None):
        """更新连接状态"""
        self.connection_status.update(
            {
                "is_connected": is_connected,
                "last_connected": datetime.now() if is_connected else self.connection_status["last_connected"],
                "error_msg": error_msg,
            }
        )

    async def _open_connection(self) -> Optional[Any]:
        """创建一个新的数据库连接（按协程 SOCKS 路由）。

        优先使用连接池；当连接池不可用时（aiomysql 缺失或创建失败）退化为单连接。
        """
        if aiomysql is None:
            return None
        # 优先返回池中连接
        pool = await self._ensure_pool()
        if pool is not None:
            # 连接在 acquire 时创建，需在 SOCKS 上下文内
            async with db_socks_context():
                try:
                    conn = await pool.acquire()
                    self._update_status(True)
                    return conn
                except Exception as e:  # pragma: no cover - acquire 失败退化
                    error_msg = f"连接池获取连接失败: {str(e)}"
                    self._update_status(False, error_msg)
                    logger.error(error_msg)
                    return None
        # 无法使用连接池，直接单连接
        async with db_socks_context():
            try:
                conn = await aiomysql.connect(  # type: ignore[attr-defined]
                    host=os.getenv(self.params.host_key),
                    port=int(os.getenv(self.params.port_key, str(self.params.default_port))),
                    user=os.getenv(self.params.user_key),
                    password=os.getenv(self.params.password_key),
                    db=os.getenv(self.params.db_key) if self.params.db_key else None,
                    charset="utf8mb4",
                    connect_timeout=self._get_connect_timeout(default_seconds=30),
                    autocommit=True,
                )
                self._update_status(True)
                return conn
            except Exception as e:
                error_msg = f"[{self.params.name}] 数据库连接失败: {str(e)}"
                self._update_status(False, error_msg)
                logger.error(error_msg)
                return None

    async def _close_connection(self, conn: Optional[Any]) -> None:
        if conn is None:
            return
        # 如果是池连接，归还到池；否则直接关闭
        pool = self.connection_pool
        if pool is not None:
            with contextlib.suppress(Exception):
                pool.release(conn)
            return
        try:
            conn.close()
        except Exception as e:
            logger.error(f"关闭连接时出错: {str(e)}")

    # 兼容方法占位（历史接口），当前无需池
    async def _close_pool(self):
        pool = self.connection_pool
        self.connection_pool = None
        if pool is not None:
            with contextlib.suppress(Exception):
                pool.close()
                await pool.wait_closed()
        self._update_status(False)

    async def _ensure_pool(self) -> Optional[Any]:
        """确保连接池已创建并可用。返回池或 None。"""
        if aiomysql is None:
            return None
        if self.connection_pool is not None:
            return self.connection_pool
        async with self._pool_lock:
            if self.connection_pool is not None:
                return self.connection_pool
            # === 连接监控保护机制 ===
            # 检查连接监控状态，防止在熔断期间创建新连接
            if not await connection_monitor.should_allow_connection(self.params.name):
                logger.warning(f"[{self.params.name}] 连接监控阻止创建新连接池（熔断状态）")
                return None

            # === 创建连接池 ===
            # 所有数据库连接都需要在SOCKS代理上下文中进行，以支持网络路由
            async with db_socks_context():
                try:
                    # 从环境变量读取连接池配置参数
                    minsize = int(os.getenv(self.params.pool_min_key, "1"))  # 最小连接数
                    maxsize = int(os.getenv(self.params.pool_max_key, "8"))  # 最大连接数
                    recycle = int(os.getenv(self.params.pool_recycle_key, "300"))  # 连接回收时间（秒）
                    connect_timeout = self._get_connect_timeout(default_seconds=30)  # 连接超时时间

                    # === 降级模式保护 ===
                    # 在降级状态下减少连接池大小，降低对数据库的压力
                    # 这是一种渐进式保护机制，避免在数据库压力大时继续施压
                    if await connection_monitor.should_use_sync_fallback(self.params.name):
                        minsize = min(minsize, 1)  # 降级时最小连接数不超过1
                        maxsize = min(maxsize, 2)  # 降级时最大连接数不超过2
                        logger.info(f"[{self.params.name}] 降级模式：减少连接池大小 min={minsize}, max={maxsize}")

                    # === 创建aiomysql连接池 ===
                    # aiomysql是异步MySQL客户端，提供连接池功能
                    pool = await aiomysql.create_pool(  # type: ignore[attr-defined]
                        host=os.getenv(self.params.host_key),  # 数据库主机
                        port=int(os.getenv(self.params.port_key, str(self.params.default_port))),  # 端口
                        user=os.getenv(self.params.user_key),  # 用户名
                        password=os.getenv(self.params.password_key),  # 密码
                        db=os.getenv(self.params.db_key) if self.params.db_key else None,  # 数据库名
                        charset="utf8mb4",  # 字符集，支持emoji等
                        autocommit=True,  # 自动提交事务
                        connect_timeout=connect_timeout,  # 连接超时
                        minsize=minsize,  # 最小连接数
                        maxsize=maxsize,  # 最大连接数
                        pool_recycle=recycle,  # 连接回收时间，防止长连接超时
                    )

                    # === 连接成功处理 ===
                    self.connection_pool = pool  # 保存连接池引用
                    self._update_status(True)  # 更新内部状态为成功

                    # 向连接监控器报告成功，用于状态恢复
                    await connection_monitor.record_success(self.params.name)

                    logger.info(
                        f"[{self.params.name}] 连接池创建成功: min={minsize}, max={maxsize}, recycle={recycle}s"
                    )
                    return pool

                except Exception as e:  # pragma: no cover - 池不可用退化
                    # === 连接失败处理 ===
                    error_msg = f"[{self.params.name}] 创建连接池失败: {str(e)}"
                    self._update_status(False, error_msg)  # 更新内部状态为失败
                    logger.error(error_msg)

                    # 向连接监控器报告失败，用于状态管理和熔断决策
                    await connection_monitor.record_failure(self.params.name, e)

                    return None

    async def test_connection(self, timeout_seconds: int = 30) -> bool:
        """测试数据库连接（严格总超时控制）。

        为避免 aiomysql 在 SOCKS 环境下可能的握手阻塞，此处采用 PyMySQL 同步测试在线程中执行，
        并通过 asyncio.wait_for 实现总超时，默认 30 秒。
        """

        async def _do_test_sync() -> bool:
            try:
                ok = await self._test_connection_via_pymysql(timeout_seconds)
                if ok:
                    logger.debug("数据库连接测试成功! (sync-fallback)")
                else:
                    logger.error("数据库连接测试失败! (sync-fallback)")
                return ok
            except Exception:
                return False

        try:
            return await asyncio.wait_for(_do_test_sync(), timeout=timeout_seconds)
        except asyncio.TimeoutError:
            logger.error(f"[{self.params.name}] 数据库连接测试超时（>{timeout_seconds}s）")
            return False

    async def execute_query(self, sql: str, themis_db: Optional[str] = None) -> SqlExecutorResult:
        """
        执行SQL查询并返回SqlExecutorResult

        Returns:
            SqlExecutorResult: 包含查询结果或错误信息
        """

        start_time = datetime.now()
        conn = None

        try:
            # 优先使用连接池执行
            pool = await self._ensure_pool()
            if pool is not None:
                async with db_socks_context():
                    conn = await pool.acquire()
                    try:
                        async with conn.cursor() as cursor:
                            await self._maybe_switch_db(cursor, themis_db)
                            await cursor.execute(sql)
                            columns = [desc[0] for desc in cursor.description]
                            rows = await cursor.fetchall()
                            results = pd.DataFrame(rows, columns=columns)
                        query_time = (datetime.now() - start_time).total_seconds()
                        logger.info(
                            f"[{self.params.name}] 查询完成，返回 {len(results)} 条记录，耗时 {query_time:.2f} 秒 (pool)"
                        )
                        return SqlExecutorResult.success(results)
                    except Exception as sql_e:
                        error_text = str(sql_e)
                        if hasattr(sql_e, "args") and len(sql_e.args) >= 2:
                            error_text = f'({sql_e.args[0]}, "{sql_e.args[1]}")'
                        error_code = self._classify_error(error_text)
                        logger.error(f"[{self.params.name}] 查询执行失败: {error_text}\nSQL如下:\n{sql}\n尝试同步后备")
                        # 尝试同步后备
                        fallback_result = await self._execute_query_via_pymysql(sql, start_time, themis_db=themis_db)
                        if fallback_result.is_successful:
                            return fallback_result
                        res = SqlExecutorResult.failure(error_text, error_code)
                        res.error_source = "connector.async"
                        return res
                    finally:
                        with contextlib.suppress(Exception):
                            pool.release(conn)

            # 无池：退化为单连接执行
            conn = await self._open_connection()
            if conn:
                try:
                    async with conn.cursor() as cursor:
                        await self._maybe_switch_db(cursor, themis_db)
                        await cursor.execute(sql)
                        columns = [desc[0] for desc in cursor.description]
                        rows = await cursor.fetchall()

                        results = pd.DataFrame(rows, columns=columns)

                    query_time = (datetime.now() - start_time).total_seconds()
                    logger.info(
                        f"[{self.params.name}] 查询完成，返回 {len(results)} 条记录，耗时 {query_time:.2f} 秒 (single)"
                    )
                    return SqlExecutorResult.success(results)
                except Exception as sql_e:
                    error_text = str(sql_e)
                    if hasattr(sql_e, "args") and len(sql_e.args) >= 2:
                        error_text = f'({sql_e.args[0]}, "{sql_e.args[1]}")'
                    error_code = self._classify_error(error_text)
                    logger.error(f"[{self.params.name}] 查询执行失败: {error_text}\nSQL如下:\n{sql}\n尝试同步后备")
                    # 尝试同步后备
                    fallback_result = await self._execute_query_via_pymysql(sql, start_time, themis_db=themis_db)
                    # 若后备也失败，返回原始错误（更准确的分类）
                    if fallback_result.is_successful:
                        return fallback_result
                    res = SqlExecutorResult.failure(error_text, error_code)
                    res.error_source = "connector.async"
                    return res
                finally:
                    await self._close_connection(conn)
            else:
                logger.error(f"[{self.params.name}] 无法获取数据库连接（异步）")
                # 继续尝试同步后备

            # === 同步后备机制 ===
            # 当异步连接完全失败时，使用同步PyMySQL + to_thread作为最后的fallback
            # 这种机制可以规避asyncio与SOCKS代理的兼容性问题
            # 虽然性能略低，但保证了系统的可用性
            return await self._execute_query_via_pymysql(sql, start_time, themis_db=themis_db)

        except Exception as e:
            # 代理或连接相关的异常
            error_text = str(e)
            error_code = self._classify_error(error_text)
            logger.error(f"[{self.params.name}] 连接或代理错误: {error_text}\nSQL如下:\n{sql}")
            res = SqlExecutorResult.failure(error_text, error_code)
            res.error_source = "connector.outer"
            return res

    async def test_connection(self) -> bool:
        """测试数据库连接是否正常"""
        try:
            result = await self.execute_query("SELECT 1 as test")
            return result.is_successful and len(result.data) > 0
        except Exception as e:
            logger.error(f"[{self.params.name}] 数据库连接测试失败: {e}")
            return False

    async def _execute_query_via_pymysql(
        self, sql: str, start_time: datetime, themis_db: Optional[str] = None
    ) -> SqlExecutorResult:
        """
        同步Fallback机制：在工作线程中运行PyMySQL，支持SOCKS路由

        ## 设计目的
        这是一个关键的fallback机制，用于解决以下问题：
        1. **异步连接池失败**：当aiomysql连接池创建失败或连接不稳定时
        2. **SOCKS代理兼容性**：避免PySocks套接字与asyncio非阻塞连接的潜在不兼容性
        3. **网络环境复杂性**：在复杂的网络环境中，同步连接可能更稳定

        ## 工作原理
        1. 使用PyMySQL（同步MySQL客户端）建立连接
        2. 通过asyncio.to_thread在线程池中执行同步操作
        3. 保持与异步接口的兼容性
        4. 提供与异步版本相同的错误处理和日志记录

        ## 性能考虑
        - 性能略低于异步版本（需要线程切换）
        - 但在异步连接失败时，这是唯一可用的选择
        - 对于大多数查询场景，性能差异可以接受

        ## 使用场景
        - 异步连接池创建失败
        - 异步查询执行异常
        - 网络环境不稳定导致的连接问题
        """
        import pymysql  # type: ignore

        def _run_sync() -> SqlExecutorResult:
            try:
                conn_timeout = self._get_connect_timeout(default_seconds=30)
                conn = pymysql.connect(
                    host=os.getenv(self.params.host_key),
                    port=int(os.getenv(self.params.port_key, str(self.params.default_port))),
                    user=os.getenv(self.params.user_key),
                    password=os.getenv(self.params.password_key),
                    database=os.getenv(self.params.db_key) if self.params.db_key else None,
                    charset="utf8mb4",
                    connect_timeout=conn_timeout,
                    read_timeout=max(conn_timeout, 60),
                    write_timeout=max(conn_timeout, 60),
                    autocommit=True,
                )
                try:
                    with conn.cursor() as cursor:
                        # THEMIS：需要切库
                        if self.params.selects_db_per_query and themis_db:
                            _db = self._validate_db(themis_db)
                            cursor.execute(f"USE `{_db}`")
                        cursor.execute(sql)
                        columns = [desc[0] for desc in cursor.description]
                        rows = cursor.fetchall()
                        results = pd.DataFrame(rows, columns=columns)
                finally:
                    try:
                        conn.close()
                    except Exception:
                        pass

                query_time = (datetime.now() - start_time).total_seconds()
                logger.info(
                    f"[{self.params.name}][sync-fallback] 查询完成，返回 {len(results)} 条记录，耗时 {query_time:.2f} 秒"
                )
                return SqlExecutorResult.success(results)
            except Exception as e:  # pragma: no cover
                error_text = str(e)
                if hasattr(e, "args") and len(e.args) >= 2:
                    error_text = f'({e.args[0]}, "{e.args[1]}")'
                error_code = self._classify_error(error_text)
                logger.error(f"[{self.params.name}][sync-fallback] 查询执行失败: {error_text}\nSQL如下:\n{sql}")
                res = SqlExecutorResult.failure(error_text, error_code)
                res.error_source = "connector.sync-fallback"
                return res

        # 确保在 DB 路径中启用 SOCKS 路由，contextvars 会传播到 to_thread 线程
        from src.langgraph.common.utils.socket_router import db_socks_context

        async with db_socks_context():
            return await asyncio.to_thread(_run_sync)

    async def _test_connection_via_pymysql(self, timeout_seconds: int | None = None) -> bool:
        import pymysql  # type: ignore

        def _run_sync_ok() -> bool:
            try:
                # 优先使用传入的超时；其次环境变量；最后默认 30
                conn_timeout = (
                    int(timeout_seconds)
                    if timeout_seconds is not None
                    else self._get_connect_timeout(default_seconds=30)
                )

                conn = pymysql.connect(
                    host=os.getenv(self.params.host_key),
                    port=int(os.getenv(self.params.port_key, str(self.params.default_port))),
                    user=os.getenv(self.params.user_key),
                    password=os.getenv(self.params.password_key),
                    database=os.getenv(self.params.db_key) if self.params.db_key else None,
                    charset="utf8mb4",
                    connect_timeout=conn_timeout,
                    read_timeout=conn_timeout,
                    write_timeout=conn_timeout,
                    autocommit=True,
                )
                try:
                    with conn.cursor() as cursor:
                        cursor.execute("SELECT 1")
                        cursor.fetchone()
                finally:
                    try:
                        conn.close()
                    except Exception:
                        pass
                return True
            except Exception:
                return False

        from src.langgraph.common.utils.socket_router import db_socks_context

        async with db_socks_context():
            return await asyncio.to_thread(_run_sync_ok)

    def _classify_error(self, error_text: str) -> str:
        """
        根据错误信息分类错误类型

        Returns:
            错误代码字符串
        """
        error_lower = error_text.lower()

        # SQL语法错误 (扩展关键词列表；合并 ocean/themis)
        sql_syntax_keywords = [
            "cannot be resolved",
            "unknown column",
            "doesn't exist",
            "syntax error",
            "sql syntax",
            "invalid syntax",
            "table doesn't exist",
            "unknown table",
            "column not found",
            "invalid column",
            "(1064,",  # MySQL/StarRocks SQL语法错误代码
            "(1146,",  # 表不存在
            "(1054,",  # 列不存在
        ]

        if any(keyword in error_lower for keyword in sql_syntax_keywords):
            return "SQL_SYNTAX_ERROR"

        # 权限错误
        permission_keywords = [
            "access denied",
            "permission denied",
            "insufficient privileges",
            "(1045,",  # MySQL访问拒绝
            "(1044,",  # 数据库访问拒绝
        ]

        if any(keyword in error_lower for keyword in permission_keywords):
            return "PERMISSION_ERROR"

        # 连接错误
        connection_keywords = [
            "connection",
            "timeout",
            "refused",
            "unreachable",
            "can't connect",
            "host not found",
            "(2003,",  # MySQL连接失败
            "(2006,",  # MySQL服务器断开
        ]

        if any(keyword in error_lower for keyword in connection_keywords):
            return "CONNECTION_ERROR"

        # 数据类型错误
        data_type_keywords = [
            "data type",
            "conversion",
            "invalid data type",
            "type mismatch",
            "(1366,",  # 数据类型错误
        ]

        if any(keyword in error_lower for keyword in data_type_keywords):
            return "DATA_TYPE_ERROR"

        # 其他未知错误
        return "UNKNOWN_ERROR"

    def _is_retryable_error(self, error_code: str) -> bool:
        """
        判断错误是否应该重试

        Returns:
            是否应该重试
        """
        # 这些错误类型不应该重试
        non_retryable_errors = {"SQL_SYNTAX_ERROR", "PERMISSION_ERROR", "DATA_TYPE_ERROR"}

        return error_code not in non_retryable_errors

    async def _maybe_switch_db(self, cursor: Any, themis_db: Optional[str]) -> None:
        """根据配置在同一连接上切换数据库（仅 THEMIS 使用）。"""
        if not self.params.selects_db_per_query:
            return
        if not themis_db:
            return
        _db = self._validate_db(themis_db)
        await cursor.execute(f"USE `{_db}`")

    def _validate_db(self, db: str) -> str:
        if self.params.allowed_dbs is not None and db not in self.params.allowed_dbs:
            raise ValueError(f"[{self.params.name}] 非法的数据库名称: {db}")
        return db

    def _get_connect_timeout(self, default_seconds: int) -> int:
        # 按优先级读取环境变量；若都不存在则使用默认
        for key in self.params.connect_timeout_keys:
            val = os.getenv(key)
            if val is not None and str(val).strip() != "":
                try:
                    return int(val)
                except Exception:
                    continue
        return default_seconds

    async def close(self):
        """关闭连接"""
        await self._close_pool()


# 全局异步数据库连接实例（最小合并：提供 ocean 与 themis 两个实例）
ocean_connection = AsyncDatabaseConnection(_build_default_ocean_params())
themis_connection = AsyncDatabaseConnection(_build_default_themis_params())
