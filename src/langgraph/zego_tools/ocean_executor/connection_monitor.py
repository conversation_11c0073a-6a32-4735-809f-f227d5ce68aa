"""数据库连接监控和保护机制

防止对线上数据库造成过度负载，提供连接健康监控和熔断机制。

## 设计理念
1. **保护线上数据库**：通过监控和熔断机制，防止异常情况下对数据库造成过度负载
2. **渐进式降级**：从健康 → 降级 → 熔断，提供多层保护
3. **自动恢复**：在条件满足时自动恢复到正常状态
4. **透明集成**：与现有连接器无缝集成，不影响正常业务逻辑

## 状态转换图
```
HEALTHY ──[连续失败达到阈值]──> DEGRADED ──[严重错误]──> CIRCUIT_OPEN
   ↑                              ↑                        ↓
   └──[连接成功]──────────────────┘         [超时后]──────┘
```

## 使用场景
- 网络不稳定导致的连接丢失
- 数据库负载过高导致的连接超时
- 临时的数据库维护或故障
- 防止雪崩效应的扩散
"""

import asyncio
import logging
import time
from dataclasses import dataclass
from enum import Enum
from typing import Dict, Optional

logger = logging.getLogger(__name__)


class ConnectionState(Enum):
    """连接状态枚举"""

    HEALTHY = "healthy"
    DEGRADED = "degraded"  # 降级状态，使用同步fallback
    CIRCUIT_OPEN = "circuit_open"  # 熔断状态，拒绝新连接


@dataclass
class ConnectionMetrics:
    """连接指标"""

    total_attempts: int = 0
    failed_attempts: int = 0
    consecutive_failures: int = 0
    last_success_time: Optional[float] = None
    last_failure_time: Optional[float] = None
    current_state: ConnectionState = ConnectionState.HEALTHY


class ConnectionMonitor:
    """数据库连接监控器

    提供连接健康监控、熔断保护和降级机制，防止对线上数据库造成过度负载。
    """

    def __init__(
        self,
        failure_threshold: int = 5,  # 连续失败阈值
        recovery_timeout: int = 60,  # 恢复超时时间（秒）
        circuit_open_duration: int = 300,  # 熔断持续时间（秒）
    ):
        self.failure_threshold = failure_threshold
        self.recovery_timeout = recovery_timeout
        self.circuit_open_duration = circuit_open_duration
        self.metrics: Dict[str, ConnectionMetrics] = {}
        self._lock = asyncio.Lock()

    async def record_success(self, connection_name: str) -> None:
        """记录连接成功"""
        async with self._lock:
            if connection_name not in self.metrics:
                self.metrics[connection_name] = ConnectionMetrics()

            metrics = self.metrics[connection_name]
            metrics.total_attempts += 1
            metrics.consecutive_failures = 0
            metrics.last_success_time = time.time()

            # 如果之前是降级状态，现在可以恢复
            if metrics.current_state == ConnectionState.DEGRADED:
                metrics.current_state = ConnectionState.HEALTHY
                logger.info(f"[{connection_name}] 连接状态恢复为健康")

    async def record_failure(self, connection_name: str, error: Exception) -> None:
        """记录连接失败"""
        async with self._lock:
            if connection_name not in self.metrics:
                self.metrics[connection_name] = ConnectionMetrics()

            metrics = self.metrics[connection_name]
            metrics.total_attempts += 1
            metrics.failed_attempts += 1
            metrics.consecutive_failures += 1
            metrics.last_failure_time = time.time()

            # 判断是否需要降级或熔断
            if metrics.consecutive_failures >= self.failure_threshold:
                if "Lost connection" in str(error) or "2013" in str(error):
                    # 连接丢失，进入降级状态
                    metrics.current_state = ConnectionState.DEGRADED
                    logger.warning(f"[{connection_name}] 连接状态降级，将使用同步fallback")
                else:
                    # 其他严重错误，进入熔断状态
                    metrics.current_state = ConnectionState.CIRCUIT_OPEN
                    logger.error(f"[{connection_name}] 连接熔断，暂停{self.circuit_open_duration}秒")

    async def should_allow_connection(self, connection_name: str) -> bool:
        """判断是否允许建立连接"""
        async with self._lock:
            if connection_name not in self.metrics:
                return True

            metrics = self.metrics[connection_name]
            current_time = time.time()

            # 熔断状态检查
            if metrics.current_state == ConnectionState.CIRCUIT_OPEN:
                if metrics.last_failure_time and current_time - metrics.last_failure_time > self.circuit_open_duration:
                    # 熔断时间结束，尝试恢复
                    metrics.current_state = ConnectionState.DEGRADED
                    metrics.consecutive_failures = 0
                    logger.info(f"[{connection_name}] 熔断结束，进入降级状态")
                    return True
                else:
                    # 仍在熔断期间
                    return False

            return True

    async def get_connection_state(self, connection_name: str) -> ConnectionState:
        """获取连接状态"""
        async with self._lock:
            if connection_name not in self.metrics:
                return ConnectionState.HEALTHY
            return self.metrics[connection_name].current_state

    async def should_use_sync_fallback(self, connection_name: str) -> bool:
        """判断是否应该使用同步fallback"""
        state = await self.get_connection_state(connection_name)
        return state in (ConnectionState.DEGRADED, ConnectionState.CIRCUIT_OPEN)

    async def get_metrics(self, connection_name: str) -> Optional[ConnectionMetrics]:
        """获取连接指标"""
        async with self._lock:
            return self.metrics.get(connection_name)


# 全局连接监控器实例
connection_monitor = ConnectionMonitor()
