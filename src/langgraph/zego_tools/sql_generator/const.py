error_code_map = {
    0: """success""",
    1: """end""",
    7: """连接媒体服务器失败""",
    3014: """'拉流报错：err_code=3014, err_message=Get Rtc AppName err: appName not found;是因为正式环境下没有开通对应权限（webrtc或者小程序权限）'""",
    60010: """文件转码出现这个问题，可能是因为没有对appsign进行转换，可以联系开发获取相关的转换代码""",
    1000404: """域名解析失败""",
    1001005: """'描述：认证失败。可能原因：AppSign 不正确。处理建议：检查传入的 AppSign 是否与 ZEGO 控制台中的 AppSign 一致。或者检查app是否已下线'""",
    1002067: """登录失败，token错误""",
    1003099: """'express错误码。海外多中心的情况下，如果他刚开始在国内连接的是国内的中心，然后他开VPN到海外，此时连接的是香港集群，但是zpush会通过SDK缓存连接到国内的中心，导致推流的时候，从香港集群找不到这个用户，会报错推流失败：用户不存在。此问题信令这边已解决。'""",
    1004007: """DNS解析失败，解析到的结果为空。""",
    1005099: """1. 检查appid定义里有没有配置混流""",
    1048677: """环境不存在。请检查appid和appsign是否填写正确""",
    1048680: """房间不存在，请检查房间是否存在。""",
    1049578: """'观众不允许创建房间。LoginRoom的时候，role设置了audience，然后又调用了setRoomConfig，接口将audienceCreateRoom的参数设置为FALSE了，导致观众无法创建房间。解决方案：将audienceCreateRoom的参数设置为TURE就可以'""",
    1050578: """OnLoginRoom返回的错误码；liveRoom的错误码对应2002，表示loginRoom的thirdPartyToken异常(setCustomToken传入)。""",
    1101001: """请求频率受限""",
    1102001: """http invalid param error""",
    1102002: """http invalid url error""",
    1102003: """http invalid buffer error""",
    1102004: """http invalid file error""",
    1102018: """token过期""",
    1102023: """1.切换网络尝试;2.检查服务器证书是否过期（2022/06/21）""",
    1103002: """浏览器访问地址不是https,或者需要用localhost访问。直接用https访问就可以。""",
    1104027: """'1. 检查推流是否正常，推流流id和拉流流id是否一致2. 如果是娃娃机，需要更新sdk到zegoliveroom_190114_182633_master-0-gdca9adda_video_bn1158_12_android，才能和express webrtc互通'""",
    1200026: """'curl: (26) Failed to open/read local data from file/application;curl错误码映射来的 本地文件读取失败'""",
    1200404: """DNS解析错误""",
    1300404: """onInitSDK返回的错误码，[CallbackCenter::OnInitSDK] error: 1300404，使用环境不对。一般情况是未配置正式环境的情况下，没有设置启用测试环境。""",
    1400001: """JSON为空""",
    1400002: """无效的JSON格式""",
    2001004: """LaunchDispatchQuery HTTP CLIENT ERROR: 2001004 代表dispatch的时候返回流不存在""",
    3040005: """attach 白板 view 失败，需要确认两个参数是否正确，一个是要渲染的目标白板ID，一个是要挂载的容器ID""",
    5000003: """'统一接入取消链接 ：例如：1、wss 和quic 并发链接，quic 成功了 wss 失败了 那么wss 就会报取消错误2、当UnInintsdk 时 链接时也会报取消错误 (2023.11月迭代后 新增)3、当缓存链接链接不上，触发旁路调度，旁路调度结果回来时，会中断当前链接，也会报取消错误(2023.11月迭代后 新增)'""",
    5000016: """'sdk 收到统一接入push 空闲主动关闭链接;用户InitSdk 后10-15s 内没有处理任何业务。服务会主动push 要sdk 断开'""",
    5200016: """kZCEAgentQUICPeerGoingAway 与对端的连接断开，正常关闭或收到服务端关闭""",
    5200025: """quic协议层心跳超时（idle timeout）""",
    5200067: """统一接入quic握手超时""",
    5201002: """'kZCEAgentQUICConnectIdleTimeout 这个表示链接空闲太长了 目前是90s。 没有使用链接，收发任何数据，会断掉'""",
    5201003: """'quic 或者wss 需要切换到更优得链接。例如： 1、quic 0rtt 链接不上，切换到wss 2、当前链接的rtt 比较大，要切换到下一个节点';netagent error, libquic error:3, QUIC_INVALID_PACKET_HEADER""",
    5244498: """quic 使用保底ip, 5S内 发送数据，服务无返回数据。""",
    5300003: """wss(tcp) 链接心跳超时导致断开;netagent error, mtcp socket closed""",
    6000230: """'因开发者业务侧审核导致消息发送失败。 1. 检查回调地址是否配置正确。2. 检查业务侧是不是在消息发送前回调中阻止了消息发送'""",
    10000101: """appid为空, not init sdk;sdk error, none appid, not init sdk""",
    10000105: """'room is not login/not login room 未loginRoom推流或拉流。';sdk error, not login room""",
    10000106: """推流bad name;sdk error, publish bad name""",
    10001001: """invalid param;sdk error, invalid param""",
    10001002: """invalid play channel, avkit sdk拉流时传入无效的通道号;sdk error, invalid play channel""",
    10001101: """没有可用的通道，拉流路数超过上限;sdk error, no free play channel""",
    10001102: """format url error;sdk error, format url error""",
    10001105: """当使用sdk 拉L3的流时，柔性配置未配置L3的拉流地址，会返回此错误码""",
    10002001: """unmatch stream id;sdk error, unmatch stream id""",
    10002002: """unmatch seq;sdk error, unmatch seq""",
    10002003: """none seq;sdk error, none seq""",
    10003001: """redirect to new url;sdk error, redirect to new url""",
    10004001: """out of memory error;sdk error, out of memory error""",
    10007001: """appid 未开发多房间权限，而调用多房间接口。请联系ZEGO技术支持""",
    10007002: """该登录的房间roomid 已经在另外一套登录接口中使用，禁止多个登录方式，登录同一个房间ID""",
    10007004: """'OnEventStopNetProbe, error:10007004;推流会导致startNetworkSpeedTest中断，报此错误码'""",
    10007005: """'OnEventStopNetProbe, error:10007005;拉流会中断startNetworkSpeedTest，报此错误码'""",
    10007010: """新版本sdk新加的，用于表示用户取消推拉流，比如用户主动停止推流、主动停止拉流、退出房间等操作。不应该算做异常错误码。不同版本有不同含义：1. (0, express3.8.0 liverrom6.24.0) 版本无此错误码;2. [express3.8.0 liverrom6.24.0, express3.9.0 liverrom6.25.0] 版本新增此错误码。用户级别推拉流请求事件(/sdk/start_publish, /sdk/start_play) ，此错误码表示取消（首次推拉流成功前，用户调用停止推拉流接口）。SDK 内部单次推拉流请求事件(/publish/start, /publish/restart, /play/start, /play/restart)，此错误码表示成功前，用户调用停止推拉流接口。推拉流事件(/sdk/publish, /cdn/publish, /rtc/publish, /p2p/publish, /sdk/play, /cdn/play, /rtc/play, /l3/play, /p2p/play)，此错误码表示用户调用停止推拉流接口，不区分是否成功过，不会上报 0。;3. [express3.10.0 liverrom6.26.0, express3.13.0 liverrom6.29.0) 版本。用户级别推拉流请求事件(/sdk/start_publish, /sdk/start_play) ，此错误码表示取消（首次推拉流成功前，用户调用停止推拉流接口）。SDK 内部单次推拉流请求事件(/publish/start, /publish/restart, /play/start, /play/restart)，此错误码表示成功前，用户调用停止推拉流接口。推拉流事件(/sdk/publish, /cdn/publish, /rtc/publish, /p2p/publish, /sdk/play, /cdn/play, /rtc/play, /l3/play, /p2p/play)，此错误码表示用户在推拉流成功前，调用停止推拉流接口；若用户在推拉流成功后调用停止推拉流接口，会上报 0。;4. [express3.13.0 liverrom6.29.0, +∞) 版本。此错误码表示用户调用停止推拉流接口时，推拉流未处于连接状态（可能还未建连成功，也可能已经失败过了还没重试成功）。""",
    10008001: """sdk没有编译调用对应模块的接口功能，请联系技术支持""",
    10009001: """物理设备错误，音频，视频，麦克风""",
    10009002: """10009002; //网络类型改变，导致推拉流重试超过最大重试时间""",
    10009004: """未使用AppSign, 也未使用Token，会直接报此错误。""",
    10009010: """' const unsigned int kSuperResolutionNotInit = 10009010; //超分未初始化;超分初始化要再初始化sdk之后'""",
    10009200: """推流使用重复通道，客户调用推流api接口 失败时上报""",
    10009201: """推流未登录房间，客户调用api 接口失败""",
    10009202: """客户拉流未登录房间，调用拉流接口失败""",
    11000101: """系统网络未连接;sdk error, network error, network not connect""",
    11000404: """DNS解析错误;sdk error, network error, dns failed""",
    11001001: """网络检测tcp connect失败(调用connect);sdk error, network error, tcp failed""",
    11001002: """网络检测tcp connect失败(超时);sdk error, network error, tcp connect failed""",
    12101001: """engine create error;sdk error, engine error, engine create error""",
    12101002: """engine status error;sdk error, engine error, engine status error""",
    12101003: """engine start error""",
    12101004: """denied max retry error;sdk error, engine error, denied max retry error""",
    12101005: """denied disable switch line error;sdk error, engine error, denied disable switch line error""",
    12102001: """拉流错误，onPlayStateUpdate(12102001)；一般情况是流已结束(未推流)或没有loginRoom就拉流。;sdk error, engine error, no play data error""",
    12102002: """客户端推udp空流logcenter上会报这个错误，但是客户那边不会有报错，SDK会内部会重复重新推流。会Publish_TempDisconnected--->Publish_BeginRetry--->Publish_RetrySuccess这样循环。;sdk error, engine error, no publish data error""",
    12102003: """'SDK调引擎拉流接口，10秒引擎都没有回调成功或失败 。2022-5-31,蜜桃出现过大量cdn拉流不存在导致这个错误'""",
    12102004: """推流建连超时""",
    12200000: """ave callback error: 0，no error;sdk error, engine callback error: -1, unknown error""",
    12200001: """ave callback error: 1，连接服务器失败;sdk error, engine callback error: 1, connect server error""",
    12200002: """ave callback error: 2，rtmp握手失败;sdk error, engine callback error: 2, rtmp handshake error""",
    12200003: """ave callback error: 3，rtmp app连接失败;sdk error, engine callback error: 3, rtmp app connect error""",
    12200004: """ave callback error: 4，rtmp 创建流失败;sdk error, engine callback error: 4, rtmp create stream error""",
    12200005: """ave callback error: 5，rtmp 推流流名不合法;sdk error, engine callback error: 5, rtmp publish bad name error""",
    12200006: """'ave callback error: 6，engine server disconnect error;拉流返回的错误码，onPlayStateUpdate(12200006)。表示从服务器(CDN)拉不到流。';sdk error, engine callback error: 6, server disconnect error""",
    12200100: """ave callback error: 100，engine rtp connect server error;sdk error, engine callback error: 100, rtp connect server error""",
    12200101: """'ave callback error: 101，engine rtp hello timeout error;引擎发送的第一个包就超时了';sdk error, engine callback error: 101, rtp hello timeout error""",
    12200102: """ave callback error: 102，engine rtp create session timeout error;sdk error, engine callback error: 102, rtp create session timeout error""",
    12200103: """ave callback error: 103，engine rtp create session fail error;sdk error, engine callback error: 103, rtp create session fail error""",
    12200104: """ave callback error: 104，engine rtp play/publish timeout error;sdk error, engine callback error: 104, rtp play/publish timeout error""",
    12200105: """ave callback error: 105，engine rtp plsy/publish denied error;sdk error, engine callback error: 105, rtp play/publish denied error""",
    12200106: """ave callback error: 106，engine rtp timeout error;sdk error, engine callback error: 106, rtp timeout error""",
    12200200: """ave callback error: 200，engine http flv protocol error, only support http 1.0/1.1;sdk error, engine callback error: 200, http flv protocol error, only support http 1.0/1.1""",
    12200201: """ave callback error: 201，engine http flv http code error;sdk error, engine callback error: 201, http flv http code error""",
    12200202: """ave callback error: 202，engine http flv parse flv error;sdk error, engine callback error: 202, http flv parse flv error""",
    12200203: """ave callback error: 203，engine http flv server disconnect error;sdk error, engine callback error: 203, http flv server disconnect error""",
    12200204: """ave callback error: 204，engine http flv redirect error;sdk error, engine callback error: 204, http flv redirect error""",
    12301001: """dispatch failed;udp error, denied error:1001, dispatch failed""",
    12301002: """empty publish ips;udp error, denied error:1002, empty publish ips""",
    12301003: """empty play ips;udp error, denied error:1003, empty play ips""",
    12301004: """'onPlayState返回的错误；表示stream not exist(流不存在)；一般情况是测试环境推流在正式环境拉流或者相反，或是确实没有推流成功。;[PlayChannel::HandlePlayDenied], chnIdx: 0, deniedInfo: {''action'':1,''err'':1004} [Jni_ZegoLiveRoomJNICallback::OnPlayStateUpdate], state=12301004 ';udp error, denied error:1004, stream not exist""",
    12301005: """app not online;udp error, denied error:1005, app not online""",
    12301006: """invalid parameters;udp error, denied error:1006, invalid parameters""",
    12301007: """no clientip set;udp error, denied error:1007, no clientip set""",
    12301008: """permission denied;udp error, denied error:1008, permission denied""",
    12301009: """set stream info failed;udp error, denied error:1009, set stream info failed""",
    12301010: """internal error;udp error, denied error:1010, internal error""",
    12301011: """publish forbidden;udp error, denied error:1011, publish forbidden""",
    12301012: """new publish;udp error, denied error:1012, new publish""",
    12301013: """inner error;udp error, denied error:1013, inner error""",
    12301014: """'客户调用后台停止推流接口时如果该流还在线则会在客户端端报 12301014;或者是客户使用了推流鉴权，Token过期导致。后台禁用该流';udp error, denied error:1014, manager stop""",
    12301015: """publish auth failed;udp error, denied error:1015, publish auth failed""",
    12301016: """play auth failed""",
    12301026: """1026：推流超限制""",
    12302004: """'A用户在推流streamID1，此时B去推流，推流的streamID和A用户推的streamID一样了，导致推流被拒绝。解决：每一端推流的streamID保持唯一就可以。';sdk error, engine denied error: 2004, vrs new publish src error""",
    12620002: """'视频解码失败,比如开启硬解，但是需要解码的分辨率码率均很低，设备/平台不支持,引擎报错以后，会自动回滚软解，不影响实际使用'""",
    15000001: """'error:15000002, reason:stop by app hibernate,app休眠导致拉流停止, sdk 检测到 app 从休眠恢复后超过最大允许重试时间'""",
    15000002: """'sdk日志里面的打印是：Stop. error:15000002, reason:stop by app hibernate,由于app休眠导致的推流停止'""",
    20000001: """柔性配置文件解密失败;flexiable error, file decrypt error""",
    20000002: """flexible offline error;flexiable error, offline error""",
    20000003: """自定义flexible域名与后台配置不一致;flexiable error, domain config error""",
    20000004: """柔性配置文件没有media_network字段;flexiable error, not exist media network config error""",
    21300404: """OnInitSDK返回的错误(21300404)；可能是AppID不存在。检查AppID是否填错了；或者是setBusinessType设置错误；或者没有配置正式环境的时候使用正式环境等。;flexiable error, http protocol error: 404, not found""",
    21300502: """OnInitDone(21300502)，柔性配置文件不存在，一般情况是环境弄错了，比如没有配置正式环境，从正式环境拉取配置文件或者配置的live，从rtv拉取配置文件。;flexiable error, http protocol error: 502, bad gateway""",
    25200000: """通过统一接入拉取柔性配置失败，一般该错误码是由于用户网络原因，导致统一接入链接不上导致。且该错误，一般在首次使用SDK，且本地没有柔性配置缓存时，在调用相关的功能接口时，在该功能接口的回调返回""",
    25720008: """通过统一接入获取柔性配置，服务返回20008 server内部错误，将该appid 联系后台同事""",
    25720012: """通过统一接入拉取柔性配置关键项失败，原因为无效appid，或者该appid未在对应环境上线""",
    25720014: """通过统一接入获取柔性配置，检测到鉴权错误。请检查appsign""",
    25720015: """appsign有问题""",
    30000001: """dispatch服务器地址无效;dispatch error, invalid dispatch server""",
    30000404: """dispatch ip为空(dispatch没报错);dispatch error, dispatch no ip""",
    31200007: """dispatch curl error: 7, Couldn't connect to server;dispatch error, http network error, curl error: 7, Couldn't connect to server""",
    31200028: """'错误意义： dispatch curl报28错误；,错误原因：客户端网络访问dispatch域名超时；,如何处理：一般情况是客户端网络原因，建议开发者更换网络后重试。';dispatch error, http network error, curl error: 28, Timeout was reached""",
    31200035: """dispatch curl error: 35, SSL connect error;dispatch error, http network error, curl error: 35, SSL connect error""",
    31200047: """dispatch curl error: 47, Number of redirects hit maximum amount""",
    32000001: """'客户端多出口ip时，会发生这个错误，不过会不影响推拉流。多出口ip情况就是：sdk发现推流ip和调度ip不一致时，你们会拿推流ip再次发起调度，这时调度会返回状态码1给sdk，sdk就会复用之前的调度结果推流，但是会返回一个调度的错误码32000001'""",
    32001001: """dispatch failed;dispatch error, server error:1001, dispatch failed""",
    32001002: """empty publish ips;dispatch error, server error:1002, empty publish ips""",
    32001003: """empty play ips;dispatch error, server error:1003, empty play ips""",
    32001004: """stream not exist 流不存在。一般情况是单点模式下，拉流的dispatch返回的结果(stream not exist)：表示流媒体服务器上没有这条流。需要确认一下推流是否正常并持续。;dispatch error, server error:1004, stream not exist""",
    32001005: """app not online;dispatch error, server error:1005, app not online""",
    32001006: """invalid parameters;dispatch error, server error:1006, invalid parameters""",
    32001007: """no clientip set;dispatch error, server error:1007, no clientip set""",
    32001008: """permission denied;dispatch error, server error:1008, permission denied""",
    32001009: """set stream info failed;dispatch error, server error:1009, set stream info failed""",
    32001010: """internal error;dispatch error, server error:1010, internal error""",
    32001011: """publish forbidden;dispatch error, server error:1011, publish forbidden""",
    32001012: """new publish;dispatch error, server error:1012, new publish""",
    32001013: """inner error;dispatch error, server error:1013, inner error""",
    32001014: """manager stop;dispatch error, server error:1014, manager stop""",
    32001015: """publish auth failed;dispatch error, server error:1015, publish auth failed""",
    32001016: """play auth failed""",
    35600401: """'信令精简版本后23年7月迭代后，表示统一接入鉴权错误。,一般出现场景：,客户使用：Token 过期，无效token,错误的appsign';dispatch error, netagent error, unauthorized""",
    40000404: """zeus后台未返回url信息;zeus error, zeus no url""",
    42000122: """当前流名已经有其他主播在推流""",
    50001001: """liveroom request invalid param;liveroom error, request invalid param""",
    50001002: """liveroom心跳超时;liveroom error, hb timeout""",
    50001003: """liveroom登录回包没有zpush信息;liveroom error, no push server addr""",
    50001004: """liveroom no push crypto key;liveroom error, no push crypto key""",
    50001005: """liveroom no push token;liveroom error, no push token""",
    50001006: """房间最大的重试时间到了导致超时""",
    50001007: """live room 激活重试时间失败""",
    50001009: """sdk内部将马上去尝试重新登录，此错误码一般onTempBroken接口返回""",
    50001010: """liveroom 发现网络已经不可用了，将会等到网络恢复后，在继续进行重试登录，此错误码一般 onTempBroken返回""",
    50001011: """用户再调用login时，正在登录中，此时又调用logout退出。此错误码一存在 speedlog 上报的sdk/login 或者sdk/relogin 事件中""",
    50001012: """此错误码表示，用户在登录房间A过程中，未调用Logout，直接登录房间B。sdk/login 会上报一次此错误码(统计成功率需求时，加的上报此错误)。一般这种错误，是用户未正常使用接口导致。""",
    50001013: """登录过程中网络切换导致中断，上报此错误码。一般多房间会触发此错误""",
    50001016: """这个错误码是指 sdk 检测到 app 从休眠恢复后超过最大允许重试时间，不做推拉流/重登录房间重试""",
    50001050: """此错误码为liveroom/hb 事件错误码，表示心跳间隔时间内(默认30s),发送心跳没有收到回包""",
    50001051: """此错误码为liveroom/hb 事件错误。当sdk已经确定 为http心跳超时了,此时单此的hb事件 未收到服务返回，则会返回此错误码。""",
    51200007: """'http单次心跳超时时会每隔3s重试一次，连续重试6次依然失败时SDK会抛出此错误码28,当请求直接不返回数据时报7';liveroom error, http network error, curl error: 7, Couldn't connect to server""",
    51200404: """有可能为 liveroom 相关域名在SDK中解析失败，SDK抛出来的;liveroom error, http network error, dns failed""",
    52000000: """success;liveroom error, server error:0, success""",
    52000001: """failure;liveroom error, server error:1, failure""",
    52000002: """input params error;liveroom error, server error:2, input params error""",
    52000003: """input params length limit;liveroom error, server error:3, input params length limit""",
    52000004: """unknown logic version;liveroom error, server error:4, unknown logic version""",
    52000005: """input name forbidden;liveroom error, server error:5, input name forbidden""",
    52000101: """auth failure;liveroom error, server error:101, auth failure""",
    52000102: """get push server error;liveroom error, server error:102, get push server error""",
    52000103: """room user to much;liveroom error, server error:103, room user to much""",
    52000104: """room not exist;liveroom error, server error:104, room not exist""",
    52000105: """user not exist;liveroom error, server error:105, user not exist""",
    52000106: """login room add error;liveroom error, server error:106, login room add error""",
    52000107: """login add online error;liveroom error, server error:107, login add online error""",
    52000108: """login add user error;liveroom error, server error:108, login add user error""",
    52000109: """logout delete online error;liveroom error, server error:109, logout delete online error""",
    52000110: """logout delete user error;liveroom error, server error:110, logout delete user error""",
    52000111: """get online index error;liveroom error, server error:111, get online index error""",
    52000112: """set room anchor error;liveroom error, server error:112, set room anchor error""",
    52000114: """set room attr error;liveroom error, server error:114, set room attr error""",
    52000115: """push msg too large;liveroom error, server error:115, push msg too large""",
    52000116: """save stream info error;liveroom error, server error:116, save stream info error""",
    52000117: """delete stream info error;liveroom error, server error:117, delete stream info error""",
    52000118: """get stream info error;liveroom error, server error:118, get stream info error""",
    52000119: """like params error;liveroom error, server error:119, like params error""",
    52000120: """save like error;liveroom error, server error:120, save like error""",
    52000121: """save chat error;liveroom error, server error:121, save chat error""",
    52000122: """get chat index error;liveroom error, server error:122, get chat index error""",
    52000123: """推流失败,具体错误原因要看liveroom日志的错误描述;liveroom error, server error:123, set streaminfo error""",
    52000124: """del streaminfo error;liveroom error, server error:124, del streaminfo error""",
    52000125: """incr streamseq error;liveroom error, server error:125, incr streamseq error""",
    52000126: """get streaminfo error;liveroom error, server error:126, get streaminfo error""",
    52000127: """cvst not exist;liveroom error, server error:127, cvst not exist""",
    52000128: """get cvst count error;liveroom error, server error:128, get cvst count error""",
    52000129: """set cvst error;liveroom error, server error:129, set cvst error""",
    52000130: """get cvst error;liveroom error, server error:130, get cvst error""",
    52000131: """add cvst member error;liveroom error, server error:131, add cvst member error""",
    52000132: """get cvst member error;liveroom error, server error:132, get cvst member error""",
    52000133: """add cvst list error;liveroom error, server error:133, add cvst list error""",
    52000134: """get cvstmsg index error;liveroom error, server error:134, get cvstmsg index error""",
    52000135: """save cvstmsg error;liveroom error, server error:135, save cvstmsg error""",
    52000136: """has not hb;liveroom error, server error:136, has not hb""",
    52000137: """set hb error;liveroom error, server error:137, set hb error""",
    52000138: """streaminfo not exist;liveroom error, server error:138, streaminfo not exist""",
    52000139: """streamid not exist;liveroom error, server error:139, streamid not exist""",
    52000141: """session error;liveroom error, server error:141, session error""",
    52000142: """new cvst id error;liveroom error, server error:142, new cvst id error""",
    52000143: """stream extra info exceed limit;liveroom error, server error:143, stream extra info exceed limit""",
    52000144: """session create error;liveroom error, server error:144, session create error""",
    52000145: """roomSeq create error;liveroom error, server error:145, roomSeq create error""",
    52000146: """format room error;liveroom error, server error:146, format room error""",
    52000147: """format user error;liveroom error, server error:147, format user error""",
    52000148: """del cvst member error;liveroom error, server error:148, del cvst member error""",
    52000149: """set stream login user error;liveroom error, server error:149, set stream login user error""",
    52000152: """room sessionid error。此错误 一般是http 心跳返回。收到此错误后，sdk内部会进行自动重连""",
    52000201: """qps limit;liveroom error, server error:201, qps limit""",
    52000202: """login qps limit;liveroom error, server error:202, login qps limit""",
    52000203: """userlist user qps limit;liveroom error, server error:203, userlist user qps limit""",
    52000204: """im user qps limit;liveroom error, server error:204, im user qps limit""",
    52000205: """cgi qps limit;liveroom error, server error:205, cgi qps limit""",
    52000207: """房间限频""",
    52000251: """third_token login num limit;liveroom error, server error:251, third_token login num limit""",
    52000252: """third_token stream num limit;liveroom error, server error:252, third_token stream num limit""",
    52000301: """broadcast error;liveroom error, server error:301, broadcast error""",
    52001001: """session error;liveroom error, server error:1001, session error""",
    52001002: """add user error;liveroom error, server error:1002, add user error""",
    52001003: """del user error;liveroom error, server error:1003, del user error""",
    52001004: """zpush conn error;liveroom error, server error:1004, zpush conn error""",
    52001005: """zpush token error;liveroom error, server error:1005, zpush token error""",
    52001006: """big room unsupport cmd;liveroom error, server error:1006, big room unsupport cmd""",
    52001008: """no msg;liveroom error, server error:1008, no msg""",
    52001009: """add trans error;liveroom error, server error:1009, add trans error""",
    52001010: """get trans update seq error;liveroom error, server error:1010, get trans update seq error""",
    52001011: """event_center not enable;liveroom error, server error:1011, event_center not enable""",
    52001012: """diff user prohibit same stream;liveroom error, server error:1012, diff user prohibit same stream""",
    52001013: """format error;liveroom error, server error:1013, format error""",
    52001014: """get trans error;liveroom error, server error:1014, get trans error""",
    52001101: """send sync enter room error;liveroom error, server error:1101, send sync enter room error""",
    52001102: """send sync leave room error;liveroom error, server error:1102, send sync leave room error""",
    52001103: """login anchor limited;liveroom error, server error:1103, login anchor limited""",
    52001104: """login room num limited；一般情况下测试环境会出现此错误，表示登录房间数超过限制。目前后台默认配置测试环境有效期3个月，房间数并发上限为10，单房间用户数上限为50。;liveroom error, server error:1104, login room num limited""",
    52001105: """login room user num limited;liveroom error, server error:1105, login room user num limited""",
    52002001: """need relogin;liveroom error, server error:2001, need relogin""",
    52002002: """third token auth error;liveroom error, server error:2002, third token auth error""",
    52002004: """'token appid类型错误,token appid type shoule be uint32'""",
    52002008: """'下面这种情况可能会报这个错误码：,开了native端登录鉴权，然后登录房间，然后客户端生成登录token时，nonce字段给的值超过了int64的范围，比如用了uint64的值，就会导致服务器这边解析json时报52002008这个错误码。'""",
    52002015: """'errorMessage : token format payload error,有一种情况是：后台开通的是权限认证token，但是客户端使用的基础认证token去登录，因此报错没有传入payload等鉴权信息。'""",
    52003001: """dati commit error;liveroom error, server error:3001, dati commit error""",
    52003002: """dati timeout error;liveroom error, server error:3002, dati timeout error""",
    52003003: """dati repeat error;liveroom error, server error:3003, dati repeat error""",
    52005021: """liveroom登录乱序被拒绝登录了。""",
    52005022: """同时登录的房间数超过最大房间个数后会返回错误码 52005022。目前默认最多同时加入 5 个房间，如有更多需求，请联系 ZEGO 技术支持提供扩展能力。""",
    52005033: """'没有支持白板的集群使用带白板功能的SDK登陆房间会报错：onLoginRoom(52005033),白板功能是特殊集群，目前没有上白板功能的客户所在集群都没有支持白板。需要配置支持白板集群才行。'""",
    52031003: """该错误码实际为31003，大概率是房间服务 zpush服务->liveroom 服务器异常，服务统一返回此错误。如发现此错误，可第一时间，找信令相关同学定位。因为该错误，不能直接反馈具体异常(后续有SDK版本，会具体解析具体错误)。""",
    52040001: """create access_token failure;liveroom error, server error:40001, create access_token failure""",
    52040002: """cgi_token invalid;liveroom error, server error:40002, cgi_token invalid""",
    52040003: """appid is empty;liveroom error, server error:40003, appid is empty""",
    52040004: """appid error;liveroom error, server error:40004, appid error""",
    52040005: """appsecret error;liveroom error, server error:40005, get appsecret error""",
    52050001: """room not exist;liveroom error, server error:50001, room not exist""",
    52050002: """user not exist;liveroom error, server error:50002, user not exist""",
    52050003: """user not online;liveroom error, server error:50003, user not online""",
    52050004: """get user error;liveroom error, server error:50004, get user error""",
    52050005: """cgi get online count error;liveroom error, server error:50005, cgi get online count error""",
    52050011: """dst user num limit;liveroom error, server error:50011, dst user num limit""",
    52050012: """msg len limit;liveroom error, server error:50012, msg len limit""",
    52050013: """send msg fail;liveroom error, server error:50013, send msg fail""",
    52050101: """dati get userlist error;liveroom error, server error:50101, dati get userlist error""",
    52050102: """dati get flowlist error;liveroom error, server error:50102, dati get flowlist error""",
    52050103: """dati get questionlist error;liveroom error, server error:50103, dati get questionlist error""",
    52050112: """发送批量命令，服务限制错误(目前服务限制10个)。 此错误 是由于登录成功之前发送流新增命令，服务限制导致. SDK 内部针对此错误会自动去重试，一般不需要关注此错误码""",
    52050113: """发送批量命令失败，一般是由于登录之前发送流新增命令失败，服务处理失败导致。此失败SDK内部会等到登录成功后重试。一般不需要关注此错误码""",
    52050114: """服务处理批量命令超时，此错误一般是流新增命令，如果SDK内部会登录成功后重试，一般不需要关注此错误码""",
    59900001: """SDK发送信令包超时(发送一个包30S内没回应，会返回此错误)。使用2022年1.09号版本后新增该错误。""",
    59900002: """由于链接被关闭了。导致发送的信令包失败。 2022.1.09后 新增此错误码""",
    59900003: """由于后台服务的重定向 ，导致发送的信令包失败。2022.1.09 号发布的版本，新增此错误码""",
    59900004: """由于链接失败，导致的在登录前的发送的信令失败，目前只有流新增。2022.1.09 后发布的版本新增此错误码""",
    59900005: """由于登录失败，登录前发送的命令失败(一般是流新增命令)。2022.1.09号 版本发布新增该错误码""",
    60000105: """zpush 没有登录""",
    60001001: """zpush tcp连接失败;zpush error, tcp connect failed""",
    60001002: """zpush发送HandShakeReq失败;zpush error, do handshake req error""",
    60001003: """zpush发送LoginReq失败;zpush error, do login req error""",
    60001004: """zpush timeout error;zpush error, timeout error""",
    60001005: """zpush心跳超时;zpush error, hb timeout error""",
    60001006: """zpush start connect error;zpush error, start connect error""",
    60001011: """zpush已经尝试了所有可用ip链接，还是无法链接成功。""",
    60001013: """zpush服务连接成功后，发送登录包，超过30s 也没有收到服务回应。""",
    60001014: """多房间发送登录请求错误，一般是由于错误参数导致。""",
    60001015: """此错误码为房间ZPush 收到OnClose 事件。导致房间断开链接。sdk内部会启动重试逻辑。 一般通过OnTempBroken 接口返回外部""",
    60001016: """此错误码为链接ZPush 服务超时，且是使用了quic才会有此错误码。出现此错误码原因可能是quic服务链接不上""",
    60001042: """多次登录时设置的用户id不一致，一般时多房间场景。例如登录A房间设置usera 登录B房间设置userb""",
    60001043: """单房间不允许登录多个房间，调用登录房间接口失败""",
    60001044: """多房间模式下，登录的房间的roomid, 已经被使用了。调用登录接口失败""",
    60001045: """退出房间的roomid 不是登录的房间id""",
    60001046: """单房间模式下，登录相同roomid""",
    60002001: """zpush invalid socket error;zpush error, invalid socket error""",
    60003001: """zpush invalid rsp error;zpush error, invalid rsp error""",
    62001016: """'在没有开通多房间功能的情况下设置了多房间模式，登录房间会失败，返回错误码 62001016。,备注(express 由于同步了LiveRoom 的最新代码，但是没有适配，多房间情况，给客户包会出现此问题。重新打包，去掉 多房间功能即可 ZEGO_ENABLE_MULTIROOM)'""",
    62002002: """zpush返回错误，第三方token错误。请检查传入的sdk的第三方token""",
    62005030: """loginroom 服务返回错误码,登录者在服务器黑名单，禁止登录""",
    62005035: """登录由服务返回,thirdtoken 在黑名单中,禁止登录""",
    62030015: """'onLoginRoom返回的错误码；原因：liveroom发送过来的biztype是2， zpush发送过来的是biztype0；需要找后台信令组解决。;[ZegoPush:1178]: [DoLoginRes] errorCode 30015, 07D908A0;[ZegoPush:1183]: DoLoginRes, parse packet failed, errorcode 30015, result 1;[CallbackCenter::OnLoginRoom] error: 62030015'""",
    62031003: """zpush相关错误；服务端Login回包报错。此错误是使用登录加速的方式登录房间。服务端登录LiveRoom时失败。SDK 收到此错误会去解析子错误码。返回给SDK外部;zpush error, server error:31003, EC_LIVEROOM_RSP_FAIL""",
    62040001: """服务器切换集群错误。SDK内部会自动强制dispatch去重试，重连。外部无感知。在错误段62040001 到62040100 中都会进行进行此处理""",
    62040100: """SDK 需要强制dispatch 的 错误码。 在错误段 62040001 - 62040100段 sdk都会强制dispatch重试""",
    62040111: """登录房间校验Token 错误。检查token 过期或者错误""",
    62050013: """sdk 重连时. 当前用户已在别的设备登录""",
    62050122: """'err:CheckToken04 userId error;生成 token 使用的 userid ，和实际登录传给SDK的 userid 不是同一个'""",
    63000001: """zpush multiple login kickout""",
    63000002: """zpush manual kickout(后台服务接口踢人)""",
    63000003: """zpush room session error""",
    63000004: """传入得Token包含房间鉴权超时，Token超时导致被T""",
    63000901: """重复登录，旧用户被kickout""",
    64000001: """发起RoomDispatch 失败。并未发起网络请求，发起动作就已经失败""",
    64000003: """房间登录时，dispatchtoken为空""",
    65200000: """统一接入使用 quic 协议，连接态 -> 断开 或者连接失败""",
    65500008: """SDK与统一接入的长连接断开""",
    65500015: """由于统一接入重定向导致的登录错误。内部sdk会立即重连""",
    65720008: """'OnLoginRoom code: 65720008 :可能是柔性配置拉失败了. 通过日志搜索flexible, 若出现 25720021 错误码. 基本为申请的appid没有测试环境,而客户设置了测试环境导致'""",
    81102003: """'混流请求使用的域名解析失败，也没有保底ip，直接发起http请求失败。一般在未使用统一接入的情况下，会报此错误'""",
    82000000: """success;mix error, server error:0, success""",
    82000001: """混流失败;mix error, server error:1, failure""",
    82000002: """input params error;mix error, server error:2, input params error""",
    82000003: """Auth Failure;mix error, server error:3, Auth Failure""",
    82000004: """parse input params error;mix error, server error:4, parse input params error""",
    82000005: """start_mix acquire lock error;mix error, server error:5, start_mix acquire lock error""",
    82000006: """stop_mix acquire lock error;mix error, server error:6, stop_mix acquire lock error""",
    82000007: """update mix_info error;mix error, server error:7, update mix_info error""",
    82000008: """create eos mix_start_req error;mix error, server error:8, create eos mix_start_req error""",
    82000009: """send eos mix_start error;mix error, server error:9, send eos mix_start error""",
    82000010: """mix_start callback error;mix error, server error:10, mix_start callback error""",
    82000011: """mix_start bypass error;mix error, server error:11, mix_start bypass error""",
    82000012: """close_owner_other_stream error;mix error, server error:12, close_owner_other_stream error""",
    82000013: """update owner error;mix error, server error:13, update owner error""",
    82000014: """create eos mix_stop_req error;mix error, server error:14, create eos mix_stop_req error""",
    82000015: """send eos mix_start error;mix error, server error:15, send eos mix_start error""",
    82000016: """mix_stop callback error;mix error, server error:16, mix_stop callback error""",
    82000017: """clear mix_state_info error;mix error, server error:17, clear mix_state_info error""",
    82000101: """Client Get Failure;mix error, server error:101, Client Get Failure""",
    82000103: """Seq Error;mix error, server error:103, Seq Error""",
    82000150: """mix stream input not exist;mix error, server error, mix stream not exist""",
    82000151: """mix stream fail;mix error, server error:151, mix stream fail""",
    82000152: """unmix stream fail;mix error, server error:152, unmix stream fail""",
    82000153: """mix stream input error;mix error, server error:153, mix stream input error""",
    82000154: """mix stream output error;mix error, server error:154, mix stream output error""",
    82000155: """mix stream input format error;mix error, server error:155, mix stream input format error""",
    82000156: """mix stream output format error;mix error, server error:156, mix stream output format error""",
    82000157: """mix not open;mix error, server error:157, mix not open""",
    82000158: """mix stream input exceed;mix error, server error:158, mix stream input exceed""",
    82000159: """mix_dispatch fail;mix error, server error:159, mix_dispatch fail""",
    82000160: """unmix owner fail;mix error, server error:160, unmix owner fail""",
    82000170: """water mark params error;mix error, server error:170, water mark params error""",
    82000171: """water mark image empty;mix error, server error:171, water mark image empty""",
    82000172: """extra_params invalid""",
    82000175: """message: 'mix stream input repeat'，混流输入流名重复""",
    82000179: """文字水印参数非法""",
    82000184: """cgi start_mix parse params error;mix error, server error:184, cgi start_mix parse params error""",
    82000185: """cgi stop_mix parse params error;mix error, server error:185, cgi stop_mix parse params error""",
    82000190: """mix_start qps overload;mix error, server error:190, mix_start qps overload""",
    82000191: """mix_battaskinfo qps overload;mix error, server error:191, mix_battaskinfo qps overload""",
    82000192: """mix_taskinfo qps overload;mix error, server error:192, mix_taskinfo qps overload""",
    82040002: """token invalid;mix error, server error:40002, token invalid""",
    82040003: """appid is empty;mix error, server error:40003, appid is empty""",
    82040004: """appid is error;mix error, server error:40004, appid is error""",
    91200077: """'CA signer not available for verification。访问https 时证书验证有问题,解决方法：1.可通过重新初始化SDK刷新证书尝试解决2.通过开启统一接入解决'""",
    92005005: """OnUpdatePublishTargetState返回的错误码(92005005)；调用addPublishTarget/delePublishTarget异常时会返回这个错误码。错误原因：在2分钟之内，先发的请求后到，后端会执行完后发的请求，丢弃先发的请求，业务侧可以忽略此错误码。""",
    100001002: """'登陆房间时，控制台输出返回result=1000001002原因： 观众角色不允许创建房间，也就是这个房间不存在。解决方案： 1）在ZegoClient.config里面的option.audienceCreateRoom设置为TRUE2）在ZegoClient.login里面将role设置为1，主播角色。'""",
    110200001: """msg：liveroom not exist，表示自动混流房间不存在，一般是网络错误异常导致，建议重试。""",
    120000001: """启动协议探测失败，无法获取探测配置数据。可能原因：配置下载失败，或者读取配置文件失败""",
    120000002: """启动网络协议探测失败，根据URL下载配置失败""",
    120000003: """网络协议探测错误：服务返回的结果解密配置失败""",
    121002002: """网络协议探测错误，探测过程中，由于网络切换，导致被中断。2020.9.09号之前由于上报错误码异常。上报错误未1002002。""",
    1000000001: """含义同推拉流10007010错误码。耗时超过5s的用户取消推拉流重试，数仓计算时会转换为1000000001错误码。""",
    1000000002: """webrtc 调用startPublishingStream方法推流时输入参数错误。可检查用户是否设置了publishOption参数，如果设置了的话，那么extrainfo参数必填。""",
    1000000101: """检查下客户appid是否过期""",
    1000000155: """web端混流报错1000000155，是 混流 输入分辨率格式错误，请检查输入分辨率格式是否正确""",
    1000000175: """'webRTC 错误码 1000000175，表示混流输入流重复导致混流异常。cmd=biz_channel，err_code=1000000175，err_message=SendChannelReq errorcode:175'""",
    1000000502: """'InitSDK返回：1000000502；[CallbackCenter::OnInitSDK] error: 1000000502,原因是：很大概率是没有配置正式环境的情况下，没有调用setUseTestEnv设置测试环境；有可能是live的情况下，调用setBusinesstype=2设置为rtv环境了。'""",
    1000001002: """appid测试环境过期web端可能报这个错误""",
    1000001006: """'小程序的报错,error:big room unsupport cmd ，该appid配置了大房间，大房间不支持野人拉取用户列表。对用户的影响就是野人拉用户列表失败'""",
    1000001009: """服务端返回的错误码是1009，详细错误码是5303。 客户端带上来的transseq 有问题，与服务端的transseq不一致""",
    1000001011: """含义同推拉流50001011错误码。登录耗时超过5s的用户取消登录请求，数仓计算时会转换为1000001011错误码。""",
    1000001101: """含义同推拉流50001011错误码。登录请求耗时超过5s的用户取消重试，数仓计算时会转换为1000001101错误码。""",
    1000010157: """ZegoClient.Error.Server , liveroom cmd error 。appid服务已过期，请检查服务过期期限。""",
    2002000009: """2002000009：setLocalDescription error 。手机上web页面报的，原因是不支持h264，可以改成vp8推流试下。""",
    2002000017: """'1. web端，房间信令通，但推拉流不通，会报这个错误。此时sdk会不断尝试切换ip地址推拉流。建议检查客户端网络，代理、防火墙等；2. 后台接口调用禁止udp直播接口，推流端返回2002000017，server session closed，拉流端收到流删除；'""",
    35500013: """推流调度相关错误，通常在推流调度过程中出现的内部错误""",
}


CONST_BLACK_CODE_PLAY = (
    10001001,
    10009200,
    10009201,
    10009202,
    60001041,
    60001042,
    60001043,
    60001044,
    60001045,
    1104001,
    1000014,
    1103049,
    12102001,
    12301004,
    12301011,
    12301014,
    32001004,
    63000001,
    63000002,
    1002055,
    1104042,
)
CONST_BLACK_CODE_LOGIN = (
    10001001,
    10009200,
    10009201,
    10009202,
    60001041,
    60001042,
    60001043,
    60001044,
    60001045,
    1100001,
    1002001,
    1002011,
    1002012,
    1002013,
    1002005,
    1002006,
    1002007,
    1002056,
    1002018,
    1002014,
    50001011,
)


CONST_BLACK_CODE_PUBLISH = (
    10001001,
    10009200,
    10009201,
    10009202,
    60001041,
    60001042,
    60001043,
    60001044,
    60001045,
    1103001,
    1103044,
    1103099,
    1000002,
    12301011,
    63000002,
    52001012,
    63000001,
    12301014,
    15000002,
)


CONST_BLACK_CODE_DISPATCH = (32001004, 35500011, 35500006, 35500013, 35500004)
