"""QFlowAgent Functional API 入口

将原有 StateGraph(Graph API) 迁移为 Functional API：
- 通过 @entrypoint orchestration 循环调度 planner → worker(并发) → reporter
- 复用原有节点实现（planner_node/worker_node/reporter_node），在任务层收敛为 state update
- 仍通过 AsyncPostgresStore / AsyncPostgresSaver 提供 store 与 checkpoint 持久化
"""

from __future__ import annotations

from contextlib import asynccontextmanager
from typing import Any, Dict, List

from langgraph.config import get_stream_writer

from langgraph.func import entrypoint
from langgraph.store.base import BaseStore

from src.langgraph.common.utils.config_utils import get_max_parallel_workers

from src.langgraph.memory.store_utils import get_store_and_checkpointer
from src.langgraph.state import append_plans, ConfigSchema
from src.langgraph.tasks import health_check_task, planner_task, reporter_task, worker_task
from src.langgraph.tasks.planner_utils import get_ready_tasks_by_results


def _apply_update(state: Dict, update: Dict) -> Dict:
    """按 State 聚合语义合并增量更新。"""
    if not update:
        return state
    # messages: 追加
    if "messages" in update and update["messages"]:
        state["messages"] = list(state.get("messages", [])) + list(update["messages"])  # 保序追加
    # tokens: 求和
    for k in ("total_input_tokens", "total_output_tokens"):
        if k in update and update[k] is not None:
            state[k] = int(state.get(k, 0)) + int(update[k])
    # plans: 采用 append_plans 语义
    if "plans" in update and update["plans"]:
        state["plans"] = append_plans(state.get("plans"), update["plans"])  # type: ignore
    # current_task: 覆盖（通常仅用于派发，不保存在最终状态）
    if "current_task" in update:
        state["current_task"] = update["current_task"]
    # dict 合并
    for k in ("worker_results", "worker_params_results"):
        if k in update and update[k]:
            merged = dict(state.get(k, {}) or {})
            merged.update(update[k])
            state[k] = merged
    # reporter 结果：覆盖
    if "reporter_result" in update and update["reporter_result"] is not None:
        state["reporter_result"] = update["reporter_result"]
    return state


@asynccontextmanager
async def zego_graph():
    """创建并返回基于 Functional API 的工作流对象（保持与原接口一致）。"""
    async with get_store_and_checkpointer() as (store, checkpointer):

        @entrypoint(checkpointer=checkpointer, store=store, config_schema=ConfigSchema, name="zego_team")
        def workflow(inputs: Dict[str, Any], *, previous: Dict[str, Any] | None = None, store: BaseStore = store):
            state: Dict[str, Any] = dict(previous or {})
            # 初始化输入
            state = _apply_update(state, inputs or {})
            writer = get_stream_writer()

            # 首次执行时进行健康检查
            if not state.get("health_check_passed", False):
                try:
                    health_upd = health_check_task(state).result()
                    state = _apply_update(state, health_upd)
                    writer(state)
                except Exception as e:
                    # 健康检查失败，终止任务执行
                    error_state = {
                        "health_check_passed": False,
                        "health_check_error": str(e),
                        "reporter_result": {"final_report": f"❌ 系统健康检查失败: {str(e)}", "error": True},
                    }
                    state = _apply_update(state, error_state)
                    writer(state)
                    return entrypoint.final(value=state, save=state)

            while True:
                plans: List = state.get("plans", []) or []
                plan = plans[-1] if plans else None

                # 首次进入或需要重规划：直接调用 planner_node 以生成/更新计划与预取
                if plan is None:
                    upd = planner_task(state).result()
                    state = _apply_update(state, upd)
                    writer(state)
                    continue  # 进入派发判断

                # 计算可执行任务
                tasks = list(getattr(plan, "tasks", []) or [])
                # TODO plan 和 result 可以合并
                ready = get_ready_tasks_by_results(tasks, state.get("worker_results", {}) or {})
                if ready:
                    limit = get_max_parallel_workers(inputs.get("config"), default=5)
                    to_run = ready[: max(1, limit)]
                    futs = []
                    for t in to_run:
                        send_state = {**state, "current_task": t}
                        futs.append(worker_task(send_state))
                    # 收敛并行结果
                    for fut in futs:
                        upd = fut.result()
                        state = _apply_update(state, upd)
                        writer(state)
                    continue

                # 无 READY 任务：判定是否结束或需要 replan
                tasks = list(getattr(plan, "tasks", []) or [])
                # 依据结果键判断是否全部完成
                results_keys = set((state.get("worker_results") or {}).keys())
                task_keys: List[str] = []
                for t in tasks:
                    try:
                        qp = getattr(getattr(t, "worker_params", None) or object(), "query_params", None)
                        if qp:
                            task_keys.append(qp.to_key())
                    except Exception:
                        continue
                all_completed_by_results = bool(task_keys) and all(k in results_keys for k in task_keys)
                all_done_by_status = bool(tasks) and all(
                    getattr(t, "status", None) in ("DONE", "FAILED") for t in tasks
                )

                if all_completed_by_results or all_done_by_status:
                    upd = reporter_task(state).result()
                    state = _apply_update(state, upd)
                    writer(state)
                    # 保存完整状态，便于跨轮查询
                    return entrypoint.final(value=state, save=state)
                else:
                    # 进入 replan（planner_node 内部会处理聚合/重规划）
                    upd = planner_task(state).result()
                    state = _apply_update(state, upd)
                    writer(state)
                    continue

        # 暴露与 Graph API 相同的使用方式：yield workflow（其支持 invoke/stream/astream）
        yield workflow
