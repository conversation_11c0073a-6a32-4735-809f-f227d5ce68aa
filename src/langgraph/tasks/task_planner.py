import logging

from langchain_core.messages import SystemMessage
from langchain_core.runnables import RunnableConfig
from langgraph.func import task

from langgraph.store.base import BaseStore

from src.langgraph.common.prompts.builder import PromptBuilder
from src.langgraph.common.prompts.fragments import (
    PLANNER_REQUIREMENTS,
    PLANNER_ROLE,
    REPLANNER_PRINCIPLES,
    REPLANNER_ROLE,
    REPLANNER_TASKS,
    SPECIAL_REGION_ISSUE_NOTES,
)

from src.langgraph.common.utils.context_builder import ContextBuilder

from src.langgraph.common.utils.llm_request_utils import create_token_update, llm_structured_request
from src.langgraph.nodes.common.data_center import DataCenter
from src.langgraph.nodes.common.types import DAGPlan, DAGPlanSchema, DAGPlanTask
from src.langgraph.state import State

from src.langgraph.tasks.dag_utils import finalize_plan
from src.langgraph.tasks.planner_utils import get_ready_tasks_by_results, validate_schema_ids

logger = logging.getLogger(__name__)


ID_FIX_INSTRUCTION = (
    "请修正上次返回的 tasks：\n"
    "- 使用从1开始连续且唯一的整数 id；\n"
    "- depends_on 仅引用这些 id；\n"
    "- 不要输出字符串依赖或标题作为键。"
)


async def _structured_schema_with_id_retry(node_name: str, base_messages: list, schema_cls):
    """请求 LLM 返回 DAGPlanSchema，并在 ID 异常时进行一次纠正重试。

    返回: (schema, total_input_tokens, total_output_tokens)
    """
    # TODO 这里检查DAG是否有环
    response_raw, schema, response_error, input_tokens, output_tokens = (
        await llm_structured_request(  # ignore unused vars
            node_name=node_name,
            input_messages=base_messages,
            schema_type=schema_cls,
        )
    )
    is_valid_ids, id_warn = validate_schema_ids(schema)
    if not is_valid_ids:
        logger.warning(f"[planner] ⚠️ 规划返回ID异常，尝试一次纠正重试:\n{id_warn}")
        fix_msg = SystemMessage(content=ID_FIX_INSTRUCTION)
        try:
            _, schema_retry, _, input_tokens2, output_tokens2 = await llm_structured_request(
                node_name=f"{node_name}_retry",
                input_messages=[*base_messages, fix_msg],
                schema_type=schema_cls,
            )
            return schema_retry, input_tokens + input_tokens2, output_tokens + output_tokens2
        except Exception:
            logger.exception("[planner] 纠正重试失败，继续使用首次返回结果")
    return schema, input_tokens, output_tokens


@task
async def planner_task(state: State):
    """
    规划与调度（含重规划与聚合）：
    - 初次进入：生成 DAG 计划并预取数据
    - 后续进入（worker→planner 回环）：当无可执行任务时，聚合判定 → 可能生成新任务或仅更新 plan.thinking；最终汇报由 reporter 兜底生成 final_report
    """
    logger.info("🤖planner node is working.")

    plans: list[DAGPlan] = state.get("plans", []) or []
    plan: DAGPlan | None = plans[-1] if plans else None
    if plan is not None:
        # 判断是否仍有可执行任务；若有则跳过聚合，交由 router 并发派发
        tasks = getattr(plan, "tasks", []) or []
        worker_results = state.get("worker_results", {}) or {}
        ready = get_ready_tasks_by_results(tasks, worker_results)
        if ready:
            return {}

    """首次规划：生成 DAG 计划并预取数据"""
    node_name = "planner"
    plans: list[DAGPlan] = state.get("plans", []) or []

    # 统一通过 ContextBuilder 构建上下文
    ctx = ContextBuilder(state=state)

    # 统一通过 PromptBuilder 构建 prompt
    pb = PromptBuilder()
    pb.add_base_analysis_role()
    pb.add_ocean_dimension_tips()
    pb.add_rtc_special_issue()
    if plans:
        pb.add_text(REPLANNER_ROLE)
        pb.add_text(REPLANNER_TASKS)
        pb.add_text(REPLANNER_PRINCIPLES)
        ctx.add_plan_goal().add_plan_thinking().add_relate_dimension_summary()
    else:
        pb.add_text(PLANNER_ROLE)
        pb.add_text(PLANNER_REQUIREMENTS)
    pb.add_text(SPECIAL_REGION_ISSUE_NOTES)

    pb.add_json_schema(
        DAGPlanSchema,
        extra_notes=(
            "- 严格遵循 schema；\n"
            "- tasks 中的 id 为从1开始连续且唯一的整数；\n"
            "- depends_on 仅包含这些整数 id；\n"
            "- 不要输出字符串依赖或以标题作为依赖键；"
        ),
    )

    prompt = pb.build()
    input_messages = ctx.build_messages(prompt=prompt)

    dag_plan_schema, input_tokens, output_tokens = await _structured_schema_with_id_retry(
        node_name, input_messages, DAGPlanSchema
    )
    state_update: dict = create_token_update(input_tokens, output_tokens)
    logger.info(f"[planner] ✅ dag_plan = \n{dag_plan_schema.model_dump_json(indent=4, exclude_none=False)}\n")
    dag_plan_schema: DAGPlanSchema = dag_plan_schema
    plan: DAGPlan = dag_plan_schema.to_plan()

    # 计划规范化（去重、依赖清洗）
    plan = finalize_plan(plan)
    if not plan.tasks:
        logger.info("[planner] 无新任务，返回空")
        return {}

    logger.info("[planner] 开始批量查询数据")
    success_count, total_count, worker_params_list = await DataCenter.batch_query_and_store_data(
        plan.to_params_list(),
        node_name,
    )
    if success_count == total_count:
        logger.info(f"[planner] ✅ 批量查询完成: 成功 {success_count}/{total_count}")
    elif success_count > 0:
        logger.warning(f"[planner] ⚠️ 批量查询部分成功: {success_count}/{total_count}")
    else:
        logger.error(f"[planner] ❌ 批量查询全部失败: {success_count}/{total_count}")

    # Hydrate the plan's placeholder WorkerParams with actual results
    for worker_params in worker_params_list:
        for plan_task in plan.tasks:
            if plan_task.worker_params.query_params.to_key() == worker_params.query_params.to_key():
                plan_task.worker_params = worker_params
    state_update.update({"plans": [plan]})
    state_update.setdefault("messages", []).append(
        plan.to_message(extra_kwargs={"input_tokens": input_tokens, "output_tokens": output_tokens})
    )
    return state_update


def planner_router(state: State, config: RunnableConfig | None = None):
    """
    统一路由（Functional 化参考）：
    - 返回 "reporter" | "planner" | (用于并发派发的待执行任务列表)
    - Graph API 的 Send/Command 在 Functional 主循环中以任务并发+合并实现
    """
    plans: list[DAGPlan] = state.get("plans", []) or []
    plan: DAGPlan | None = plans[-1] if plans else None

    # 统一出口
    next_node = "planner"
    dispatch_tasks: list[DAGPlanTask] | None = None

    # 默认统计（仅日志用途）
    total_tasks = 0
    status_counts = {"READY": 0, "PENDING": 0, "RUNNING": 0, "DONE": 0, "FAILED": 0}

    if plan is None:
        next_node = "planner"
    else:
        tasks = getattr(plan, "tasks", []) or []
        total_tasks = len(tasks)

        # 计算可执行任务集（基于已完成结果）
        worker_results = state.get("worker_results", {}) or {}
        ready = get_ready_tasks_by_results(tasks, worker_results)

        # 统计任务状态
        for t in tasks:
            st = getattr(t, "status", None) or "PENDING"
            if st in status_counts:
                status_counts[st] += 1

        if not ready:
            # 判断是否全部完成
            results_keys = set((worker_results or {}).keys())
            task_keys: list[str] = []
            for t in tasks:
                try:
                    qp = getattr(getattr(t, "worker_params", None) or object(), "query_params", None)
                    if qp:
                        task_keys.append(qp.to_key())
                except Exception:
                    continue
            all_completed_by_results = bool(task_keys) and all(k in results_keys for k in task_keys)

            # 兼容既有逻辑：若显式标记为 DONE/FAILED 也应进入 reporter
            all_done_by_status = bool(tasks) and all(getattr(t, "status", None) in ("DONE", "FAILED") for t in tasks)

            if all_completed_by_results or all_done_by_status:
                next_node = "reporter"
            else:
                next_node = "planner"
        else:
            dispatch_tasks = ready
            next_node = "worker"

    # 统一日志
    logger.info(f"[router] 任务状态: {status_counts}")
    try:
        if plan is None:
            logger.info("[router] 计划不存在 → 前往节点: planner")
        else:
            base = f"[router] 任务状态统计: 总数 {total_tasks}"
            if next_node == "worker" and dispatch_tasks is not None:
                logger.info(f"{base} → 前往: worker，派发 {len(dispatch_tasks)} 个任务")
            else:
                logger.info(f"{base} → 前往: {next_node}")
    except Exception:
        pass

    return dispatch_tasks if next_node == "worker" else next_node
