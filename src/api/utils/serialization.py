"""序列化工具

提供安全的对象序列化功能，特别是处理复杂的Pydantic模型和不可序列化的对象。
"""

import json
import logging
from datetime import date, datetime
from decimal import Decimal
from pathlib import Path
from typing import Any

import pandas as pd
from pydantic import BaseModel

# 导入现有的时间格式化工具
from src.langgraph.common.utils.time_utils import format_human_datetime

# 尝试导入pandas的Timestamp类型
try:
    from pandas import Timestamp

    HAS_PANDAS_TIMESTAMP = True
except ImportError:
    HAS_PANDAS_TIMESTAMP = False

logger = logging.getLogger(__name__)


def _is_timestamp_like(obj) -> bool:
    """检查对象是否是时间戳类型（优化版本）"""
    if obj is None:
        return False

    # 优化：直接检查类型名称，避免重复的字符串操作
    type_name = type(obj).__name__.lower()

    # 常见的时间戳类型检查
    return "timestamp" in type_name or "datetime" in type_name or "time" in type_name


def _safe_convert_timestamp(obj) -> str:
    """安全地转换时间戳对象为字符串，使用现有的format_human_datetime函数"""
    # 调试日志（使用DEBUG级别，避免生产环境性能影响）
    logger.debug(f"Converting timestamp object: {type(obj)} = {obj}")

    try:
        # 使用现有的format_human_datetime函数，它能处理各种时间戳类型
        result = format_human_datetime(obj)
        logger.debug(f"format_human_datetime success: {result}")
        return result
    except Exception as e:
        logger.debug(f"format_human_datetime failed: {e}")
        # 如果format_human_datetime失败，尝试其他方法
        try:
            if hasattr(obj, "isoformat") and callable(getattr(obj, "isoformat")):
                result = obj.isoformat()
                if isinstance(result, str):
                    logger.debug(f"isoformat success: {result}")
                    return result
        except Exception as e2:
            logger.debug(f"isoformat failed: {e2}")
            pass

        try:
            if hasattr(obj, "strftime") and callable(getattr(obj, "strftime")):
                result = obj.strftime("%Y-%m-%d %H:%M:%S")
                logger.debug(f"strftime success: {result}")
                return result
        except Exception as e3:
            logger.debug(f"strftime failed: {e3}")
            pass

        try:
            result = str(obj)
            logger.debug(f"str() success: {result}")
            return result
        except Exception as e4:
            logger.warning(f"All timestamp conversion methods failed: {e4}")
            return "<timestamp_conversion_failed>"


def safe_serialize(obj: Any, max_depth: int = 10, current_depth: int = 0) -> Any:
    """安全序列化对象，处理各种不可序列化的类型

    Args:
        obj: 要序列化的对象
        max_depth: 最大递归深度，防止无限递归
        current_depth: 当前递归深度

    Returns:
        可序列化的对象
    """
    if current_depth > max_depth:
        return f"<max_depth_exceeded:{type(obj).__name__}>"

    # 基本类型直接返回
    if obj is None or isinstance(obj, (bool, int, float, str)):
        return obj

    # 日期时间类型处理（优先级最高）
    if isinstance(obj, (datetime, date)):
        return obj.isoformat()

    # 通用时间戳处理（使用新的检测和转换函数）
    if _is_timestamp_like(obj):
        return _safe_convert_timestamp(obj)

    # pandas Timestamp类型（具体类型检查，作为备用）
    if HAS_PANDAS_TIMESTAMP:
        try:
            if isinstance(obj, Timestamp):
                return _safe_convert_timestamp(obj)
        except Exception:
            pass

    # Decimal类型
    if isinstance(obj, Decimal):
        return float(obj)

    # Path类型
    if isinstance(obj, Path):
        return str(obj)

    # Pydantic模型（特殊处理）
    if isinstance(obj, BaseModel):
        try:
            # 特殊处理DAGPlan和相关类型
            if hasattr(obj, "__class__") and "DAGPlan" in obj.__class__.__name__:
                # DAGPlan类型需要特殊处理，因为包含复杂的嵌套结构
                return {
                    "_type": "DAGPlan",
                    "goal": getattr(obj, "goal", ""),
                    "thinking": getattr(obj, "thinking", ""),
                    "tasks_count": len(getattr(obj, "tasks", [])),
                    "tasks_summary": [
                        {
                            "id": getattr(task, "id", None),
                            "status": getattr(task, "status", "unknown"),
                            "depends_on": getattr(task, "depends_on", []),
                            "query_title": (
                                getattr(getattr(task, "worker_params", None), "query_params", {}).get("query_title", "")
                                if hasattr(task, "worker_params")
                                else ""
                            ),
                        }
                        for task in (getattr(obj, "tasks", []) or [])[:10]  # 限制显示前10个任务
                    ],
                }

            # 使用model_dump获取字典表示
            model_data = obj.model_dump()
            return safe_serialize(model_data, max_depth, current_depth + 1)
        except Exception as e:
            logger.warning(f"Failed to serialize Pydantic model {type(obj).__name__}: {e}")
            # 提供更详细的错误信息
            try:
                # 尝试获取基本信息
                basic_info = {"_type": type(obj).__name__, "_error": f"serialization_failed: {str(e)}"}
                # 尝试获取一些基本属性
                for attr in ["id", "name", "title", "goal", "status"]:
                    if hasattr(obj, attr):
                        try:
                            basic_info[attr] = str(getattr(obj, attr))
                        except Exception:
                            pass
                return basic_info
            except Exception:
                return {"_type": type(obj).__name__, "_error": f"complete_serialization_failed: {str(e)}"}

    # pandas DataFrame
    if isinstance(obj, pd.DataFrame):
        try:
            # 转换为字典格式，限制行数防止过大
            if len(obj) > 1000:
                logger.warning(f"DataFrame too large ({len(obj)} rows), truncating to 1000 rows")
                obj = obj.head(1000)

            # 处理DataFrame中的时间戳列（更强大的处理机制）
            df_copy = obj.copy()
            for col in df_copy.columns:
                try:
                    # 处理明确的datetime类型
                    if df_copy[col].dtype == "datetime64[ns]" or "datetime" in str(df_copy[col].dtype):
                        df_copy[col] = df_copy[col].dt.strftime("%Y-%m-%d %H:%M:%S")

                    # 处理object类型列（可能包含Timestamp对象）
                    elif df_copy[col].dtype == "object" and len(df_copy) > 0:
                        # 更高效地检查第一个非空值是否是时间戳类型
                        non_null_series = df_copy[col].dropna()
                        if not non_null_series.empty:
                            first_non_null = non_null_series.iloc[0]

                            # 检查是否是时间戳类型
                            if _is_timestamp_like(first_non_null):
                                # 应用安全的时间戳转换
                                def safe_timestamp_convert(x):
                                    if x is None or pd.isna(x):
                                        return None
                                    return _safe_convert_timestamp(x)

                                df_copy[col] = df_copy[col].apply(safe_timestamp_convert)

                except Exception as e:
                    # 如果列处理失败，转为字符串
                    logger.warning(f"Failed to process column {col}: {e}")
                    try:
                        df_copy[col] = df_copy[col].astype(str)
                    except Exception:
                        # 最后的兜底：用占位符替换
                        df_copy[col] = f"<serialization_error_column_{col}>"

            return {
                "_type": "DataFrame",
                "data": df_copy.to_dict(orient="records"),
                "columns": list(df_copy.columns),
                "shape": df_copy.shape,
            }
        except Exception as e:
            logger.warning(f"Failed to serialize DataFrame: {e}")
            return {
                "_type": "DataFrame",
                "_error": f"serialization_failed: {str(e)}",
                "shape": getattr(obj, "shape", "unknown"),
            }

    # pandas Series
    if isinstance(obj, pd.Series):
        try:
            return {"_type": "Series", "data": obj.to_dict(), "name": obj.name}
        except Exception as e:
            logger.warning(f"Failed to serialize Series: {e}")
            return {"_type": "Series", "_error": f"serialization_failed: {str(e)}"}

    # 字典类型
    if isinstance(obj, dict):
        result = {}
        for key, value in obj.items():
            try:
                # 确保key是字符串
                safe_key = str(key) if not isinstance(key, str) else key
                result[safe_key] = safe_serialize(value, max_depth, current_depth + 1)
            except Exception as e:
                logger.warning(f"Failed to serialize dict key {key}: {e}")
                result[str(key)] = f"<serialization_error: {str(e)}>"
        return result

    # 列表和元组类型
    if isinstance(obj, (list, tuple)):
        result = []
        for i, item in enumerate(obj):
            try:
                result.append(safe_serialize(item, max_depth, current_depth + 1))
            except Exception as e:
                logger.warning(f"Failed to serialize list item {i}: {e}")
                result.append(f"<serialization_error: {str(e)}>")
        return result

    # 集合类型
    if isinstance(obj, set):
        try:
            return list(safe_serialize(list(obj), max_depth, current_depth + 1))
        except Exception as e:
            logger.warning(f"Failed to serialize set: {e}")
            return f"<serialization_error: {str(e)}>"

    # 其他对象类型
    try:
        # 尝试获取对象的__dict__
        if hasattr(obj, "__dict__"):
            return {"_type": type(obj).__name__, **safe_serialize(obj.__dict__, max_depth, current_depth + 1)}
        # 尝试转换为字符串
        else:
            return {"_type": type(obj).__name__, "_str": str(obj)}
    except Exception as e:
        logger.warning(f"Failed to serialize object {type(obj).__name__}: {e}")
        return {"_type": type(obj).__name__, "_error": f"serialization_failed: {str(e)}"}


def safe_json_dumps(obj: Any, **kwargs) -> str:
    """安全的JSON序列化，使用safe_serialize预处理对象

    Args:
        obj: 要序列化的对象
        **kwargs: 传递给json.dumps的其他参数

    Returns:
        JSON字符串
    """
    try:
        # 先进行安全序列化预处理
        safe_obj = safe_serialize(obj)

        # 设置默认参数
        default_kwargs = {"ensure_ascii": False, "indent": None, "separators": (",", ":")}  # 紧凑格式
        default_kwargs.update(kwargs)

        return json.dumps(safe_obj, **default_kwargs)
    except Exception as e:
        logger.error(f"Failed to serialize object to JSON: {e}")
        # 返回错误信息的JSON
        error_obj = {"error": "serialization_failed", "message": str(e), "type": type(obj).__name__}
        return json.dumps(error_obj, ensure_ascii=False)


def truncate_large_data(obj: Any, max_size: int = 10000) -> Any:
    """截断过大的数据，防止序列化结果过大

    Args:
        obj: 要处理的对象
        max_size: 最大字符串长度

    Returns:
        处理后的对象
    """
    if isinstance(obj, str) and len(obj) > max_size:
        return obj[:max_size] + f"...<truncated, original length: {len(obj)}>"

    if isinstance(obj, dict):
        result = {}
        for key, value in obj.items():
            result[key] = truncate_large_data(value, max_size)
        return result

    if isinstance(obj, (list, tuple)):
        return [truncate_large_data(item, max_size) for item in obj]

    return obj
