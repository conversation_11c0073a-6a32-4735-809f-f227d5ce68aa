"""FastAPI主应用。"""

import logging
from contextlib import asynccontextmanager

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from src.api.checkpoint_manager import checkpoint_manager
from src.api.routes import tasks
from src.langgraph.common.init_log import init_log

# 初始化日志
init_log()
logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理。"""
    # 启动时初始化
    logger.info("Starting QFlowAgent API server...")
    try:
        await checkpoint_manager.initialize()
        logger.info("CheckpointManager initialized successfully")
    except Exception as e:
        logger.error(f"Failed to initialize CheckpointManager: {e}")
        raise

    yield

    # 关闭时清理
    logger.info("Shutting down QFlowAgent API server...")


# 创建FastAPI应用
app = FastAPI(title="QFlowAgent API", description="QFlowAgent Web接口服务", version="1.0.0", lifespan=lifespan)

# 配置CORS
app.add_middleware(
    CORSMiddleware,
    allow_origins=[
        "http://localhost:3000",
        "http://localhost:3001",
        "http://localhost:5173",
    ],  # Vue开发服务器支持端口轮换
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# 注册路由
app.include_router(tasks.router)


@app.get("/")
async def root():
    """根路径。"""
    return {"message": "QFlowAgent API Server", "version": "1.0.0", "docs": "/docs"}


@app.get("/health")
async def health_check():
    """健康检查。"""
    return {"status": "healthy"}


if __name__ == "__main__":
    import uvicorn

    uvicorn.run("src.api.main:app", host="0.0.0.0", port=8000, reload=True, log_level="info")
