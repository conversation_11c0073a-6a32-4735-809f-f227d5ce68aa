{
    // Use IntelliSense to learn about possible attributes.
    // Hover to view descriptions of existing attributes.
    // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
    "version": "0.2.0",
    "configurations": [
      
        {
            "name": "zego_cli",
            "type": "debugpy",
            "envFile": "${workspaceFolder}/.env",
            "request": "launch",
            "program": "${workspaceFolder}/apps/zego_cli.py",
            "console": "integratedTerminal",
            "justMyCode": false
        },
        {
            "name": "zego_lark",
            "type": "debugpy",
            "envFile": "${workspaceFolder}/.env",
            "request": "launch",
            "program": "${workspaceFolder}/apps/zego_lark.py",
            "console": "integratedTerminal",
            "justMyCode": false
        },  
        {
            "name": "server",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/.venv/bin/uvicorn",
            "args": [
                "src.fastapi.main:app",
                "--reload",
                "--port",
                "2026"
            ],
            "console": "integratedTerminal",
            "envFile": "${workspaceFolder}/.env"
        },
        {
            "name": "cli_stream",
            "type": "debugpy",
            "request": "launch",
            "program": "${workspaceFolder}/apps/api_cli.py",
            "args": [
                "--action",
                "stream",
                "--input",
                "分析 1850816294 拉流成功率"
            ],
            "console": "integratedTerminal",
            "envFile": "${workspaceFolder}/.env"
        },
        {
            "name": "zego_themis",
            "type": "debugpy",
            "envFile": "${workspaceFolder}/.env",
            "request": "launch",
            "program": "${workspaceFolder}/apps/zego_themis.py",
            "console": "integratedTerminal",
            "justMyCode": false,
            "args": ["--type", "ocean", "--db", "sdk", "--sql", "SELECT 1"]
        },
        {
            "name": "test_sqlgen",
            "type": "debugpy",
            "envFile": "${workspaceFolder}/.env",
            "request": "launch",
            "program": "${workspaceFolder}/tests/src/test_sqlgen.py",
            "console": "integratedTerminal",
            "justMyCode": false,
            "args": ["--data-dir", "${workspaceFolder}/tests/data", "generate-daily"]
        },  
        {
            "name": "lark_send_msg",
            "type": "debugpy",
            "envFile": "${workspaceFolder}/.env",
            "request": "launch",
            "program": "${workspaceFolder}/src/lark/api_send_msg.py",
            "console": "integratedTerminal",
            "justMyCode": false
        },        
        {
            "name": "test_kimi_v2",
            "type": "debugpy",
            "envFile": "${workspaceFolder}/.env",
            "request": "launch",
            "program": "${workspaceFolder}/tests/test_kimi_k2.py",
            "console": "integratedTerminal",
            "justMyCode": false
        },
        {
            "name": "sqlgen",
            "type": "debugpy",
            "envFile": "${workspaceFolder}/.env",
            "request": "launch",
            "program": "${workspaceFolder}/src/teams/zego_team/tools/sqlgen.py",
            "console": "integratedTerminal",
            "justMyCode": false
        },
        {
            "name": "zego_test_fewshot",
            "type": "debugpy",
            "envFile": "${workspaceFolder}/.env",
            "request": "launch",
            "program": "${workspaceFolder}/src/teams/zego_team/metrics/metric_prompt.py",
            "console": "integratedTerminal",
            "justMyCode": false
        },
        {
            "name": "resume",
            "type": "debugpy",
            "envFile": "${workspaceFolder}/.env",
            "request": "launch",
            "program": "${workspaceFolder}/apps/resume.py",
            "console": "integratedTerminal",
            "justMyCode": false
        }, 
        {
            "name": "store_test",
            "type": "debugpy",
            "envFile": "${workspaceFolder}/.env",
            "request": "launch",
            "program": "${workspaceFolder}/src/memory/store_test.py",
            "console": "integratedTerminal",
            "justMyCode": false
        },    
        {
            "name": "test_cot_llm",
            "type": "debugpy",
            "envFile": "${workspaceFolder}/.env",
            "request": "launch",
            "program": "${workspaceFolder}/tests/test_cot_llm.py",
            "console": "integratedTerminal",
            "justMyCode": false
        },        
        {
            "name": "writer_store_test",
            "type": "debugpy",
            "envFile": "${workspaceFolder}/.env",
            "request": "launch",
            "program": "${workspaceFolder}/src/teams/writer_team/store/test_writer_store.py",
            "console": "integratedTerminal",
            "justMyCode": false
        },      {
            "name": "vector_query",
            "type": "debugpy",
            "envFile": "${workspaceFolder}/.env",
            "request": "launch",
            "program": "${workspaceFolder}/apps/vector_query.py",
            "console": "integratedTerminal",
            "justMyCode": false
        },   
        {
            "name": "test_progress_simple",
            "type": "debugpy",
            "envFile": "${workspaceFolder}/.env",
            "request": "launch",
            "program": "${workspaceFolder}/src/teams/writer_team/test_progress_simple.py",
            "console": "integratedTerminal",
            "justMyCode": false
        },        {
            "name": "test_tool_node",
            "type": "debugpy",
            "envFile": "${workspaceFolder}/.env",
            "request": "launch",
            "program": "${workspaceFolder}/tests/test_tool_node.py",
            "console": "integratedTerminal",
            "justMyCode": false
        },        
        // ,{
        //     "name": "langgraph",
        //     "type": "debugpy",
        //     "envFile": "${workspaceFolder}/.env",
        //     "console": "integratedTerminal",
        //     "request": "launch",
        //     "module": "langgraph",
        //     "args": ["dev"]
        // }
    ]
}